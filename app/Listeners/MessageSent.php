<?php

namespace App\Listeners;

use App\Events\MessageSent as MessageSentEvent;
use Illuminate\Contracts\Queue\ShouldQueue;

class MessageSent implements ShouldQueue
{
    /**
     * Create the event listener.
     */
    public function __construct(){

    }

    /**
     * Handle the event.
     */
    public function handle(MessageSentEvent $event): void
    {
        $order = $event->message->order;
        $message = $event->message;
        $item = $message->item;

        // Update order status
        if(!$order->is_message_sent) {
            $order->is_message_sent = true;
            $order->save();
        }

        //Update item status
        if($item && !$item->message_sent) {
            $item->message_sent = true;
            $item->save();
        }
    }
}
