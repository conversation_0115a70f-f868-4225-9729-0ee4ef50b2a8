<?php

namespace App\Listeners;

use Illuminate\Support\Facades\Log;
use Osiset\ShopifyApp\Messaging\Events\ShopAuthenticatedEvent;

class ShopifyShopAuthenticated
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(ShopAuthenticatedEvent $event): void
    {
        $locale = request()->get('locale');
        $availableLocales = config('app.available_locales', []);
        if ($locale && in_array($locale, $availableLocales)) {
            app()->setLocale($locale);
        } else {
            app()->setLocale(config('app.fallback_locale', 'en'));
        }

        $storeId = $event->shopId->toNative();
        $store = \App\Models\User::where('id', $storeId)->first();
        if ($store) {
            $store->locale = app()->getLocale();
            $store->save();
        }
    }
}
