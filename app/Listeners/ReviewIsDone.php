<?php

namespace App\Listeners;

use App\Enums\Settings;
use App\Events\ReviewIsDoneEvent;
use App\Jobs\PublishReview;
use Illuminate\Contracts\Queue\ShouldQueue;

class ReviewIsDone implements ShouldQueue
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(ReviewIsDoneEvent $event): void
    {
        $review = $event->review;
        $user = $review->user;
        if(!$user->hasPermissionTo(\App\Enums\Permissions\Review::PUBLISH->value)) {
            return;
        }

        if($user->getSetting(Settings::AUTO_REVIEW_PUBLISH->value) !== true) {
            return;
        }

        if($review->rating < $user->getSetting(Settings::MINIMUM_RATE_FOR_AUTO_PUBLISH->value)) {
            return;
        }

        PublishReview::dispatch($review);
    }
}
