<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Services\User\Defaults\DefaultSettingsGenerator;
use App\Services\User\Defaults\DefaultTemplatesGenerator;
use Illuminate\Console\Command;

class RunDefaults extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:run-defaults {user?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userId = $this->argument('user');
        if($userId) {
            $user = User::find($userId);
            if(!$user) {
                $this->error('User not found');
                return;
            }
            $this->runDefaults($user);
        } else {
            $users = User::all();
        }

        foreach ($users as $user) {
            $this->runDefaults($user);
        }
    }

    private function runDefaults($user)
    {
        (new DefaultSettingsGenerator())->execute($user);
        (new DefaultTemplatesGenerator())->execute($user);
    }
}
