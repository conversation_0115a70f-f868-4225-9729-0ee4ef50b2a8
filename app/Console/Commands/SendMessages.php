<?php

namespace App\Console\Commands;

use App\Enums\Permissions\Message;
use App\Enums\Settings;
use App\Jobs\SendMessageJob;
use App\Models\Order;
use App\Models\User;
use App\Services\AppSettings;
use App\Services\User\GetBlackListedPhoneNumbers;
use Illuminate\Console\Command;

class SendMessages extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:send-messages';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send messages for all orders';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $users = User::all();
        foreach ($users as $user) {
            if (!AppSettings::permissionCheck(Message::AUTO_SEND->value, $user)) {
                continue;
            }

            // Check if the user has auto message sending enabled
            if (!$user->getSetting(Settings::AUTO_MESSAGE_SEND->value)) {
                continue;
            }

            $now = now();
            $messageSendTime = $user->getSetting(Settings::AUTO_MESSAGE_SEND_TIME->value);
            $timezone = $user->getSetting(Settings::TIMEZONE->value);

            if (!$messageSendTime || !$timezone) {
                continue;
            }

            $now = $now->setTimezone($timezone);
            $messageSendTime = $now->copy()->setTimeFromTimeString($messageSendTime);
            if ($now->diffInMinutes($messageSendTime) > 5 || $now->diffInMinutes($messageSendTime) < -5) {
                continue;
            }

            $days = $user->getSetting(Settings::MESSAGE_SEND_AFTER_DAYS->value);
            $orders = Order::where('user_id', $user->id)
                ->where('is_message_sent', false)
                ->where('is_in_queue', true)
                ->where('order_created_at', '>=', $now->subDays($days)->startOfDay())
                ->get();

            $blackListedPhoneNumbers = GetBlackListedPhoneNumbers::execute($user);

            foreach ($orders as $order) {
                // Check if the order's phone number is blacklisted
                if (in_array($order->phone_number, $blackListedPhoneNumbers)) {
                    continue;
                }

                SendMessageJob::dispatch($order);
            }
        }
    }
}
