<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;

class CreateSuperAdminUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'superadmin:create
                            {--name= : The full name of the super admin user}
                            {--email= : The email address of the super admin user}
                            {--password= : The password for the super admin user}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Verify no existing admin with this email
        if (User::where('is_super_admin', true)->exists()) {
            $this->error('A super admin user already exists.');
            return self::FAILURE;
        }

        // Collect input with validation
        $name = $this->option('name') ?? $this->ask('Super Admin full name');
        $email = $this->option('email') ?? $this->ask('Super Admin email address');
        $password = $this->option('password') ?? $this->ask('Super Admin password', null, function ($answer) {
            if (empty($answer)) {
                $this->error('Password cannot be empty!');
                return false; // Prevent empty password
            }
            return $answer;
        });

        // Create admin user (bypassing mass assignment)
        $admin = new User();
        $admin->name = $name;
        $admin->email = $email;
        $admin->password = Hash::make($password);
        $admin->is_super_admin = true;
        $admin->email_verified_at = now();
        $admin->save();

        // Output results
        $this->line('');
        $this->line('<fg=white;bg=blue> ADMIN USER CREATED </>');
        $this->line("Name: <comment>{$name}</comment>");
        $this->line("Email: <comment>{$email}</comment>");
        $this->line("Password: <comment>{$password}</comment>");
        $this->line('');
        $this->line('<fg=red>IMPORTANT: Store this password securely and change it immediately!</>');
        
        return self::SUCCESS;
    }
}
