<?php

namespace App\Providers\Socialite;

use <PERSON><PERSON>\Socialite\Two\AbstractProvider;
use <PERSON><PERSON>\Socialite\Two\User;

class JudgeMeProvider extends AbstractProvider
{
    protected $scopeSeparator = ' ';
    protected $scopes = ['read_products', 'write_products'];

    protected function getAuthUrl($state)
    {
        return $this->buildAuthUrlFromBase('https://app.judge.me/oauth/authorize', $state);
    }

    protected function getTokenUrl()
    {
        return 'https://app.judge.me/oauth/token';
    }

    protected function getUserByToken($token)
    {
        $response = $this->getHttpClient()->get('https://api.judge.me/api/v1/user', [
            'headers' => [
                'Authorization' => 'Bearer ' . $token,
                'Accept' => 'application/json',
            ],
        ]);

        return json_decode($response->getBody(), true)['user'];
    }

    protected function mapUserToObject(array $user)
    {
        return (new User())->setRaw($user)->map([
            'id' => $user['id'],
            'nickname' => null,
            'name' => $user['name'] ?? null,
            'email' => $user['email'] ?? null,
            'avatar' => $user['avatar'] ?? null,
        ]);
    }
}
