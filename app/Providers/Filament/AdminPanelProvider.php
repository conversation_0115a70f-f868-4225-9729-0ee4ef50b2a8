<?php

namespace App\Providers\Filament;

use App\Filament\Widgets\LastMessages;
use App\Filament\Widgets\StatsOverview;
use App\Http\Middleware\Billable;
use App\Http\Middleware\FrameHeadersMiddleware;
use App\Http\Middleware\VerifyShopifyCopy;
use App\Services\AppSettings;
use App\Services\Shops\Shopify\CheckIfShopifyRequest;
use BezhanSalleh\FilamentLanguageSwitch\LanguageSwitch;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Pages;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\Support\Facades\FilamentView;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\HtmlString;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use Osiset\ShopifyApp\Util;

class AdminPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        $panel->default()
            ->id('admin')
            ->path('admin')
            ->login()
            // ->viteTheme('resources/css/filament/admin/theme.css')
            ->colors([
                'primary' => Color::Amber,
            ])
            ->discoverResources(in: app_path('Filament/Resources'), for: 'App\\Filament\\Resources')
            ->discoverPages(in: app_path('Filament/Pages'), for: 'App\\Filament\\Pages')
            ->pages([
                Pages\Dashboard::class,
            ])
            ->widgets([
                StatsOverview::class,
                LastMessages::class
            ])
            ->globalSearch(false)
            ->topNavigation()
            ->spa()
            ->colors([
                'primary' => '#25D366', // WhatsApp green
                'secondary' => '#075E54', // Darker WhatsApp green/teal
                'accent' => '#128C7E', // WhatsApp button/link blue-green
                'success' => '#34B7F1', // WhatsApp light blue
            ])
            ->databaseNotifications()
            ->registration();

        if (CheckIfShopifyRequest::execute()) {
            return $panel->middleware([
                DisableBladeIconComponents::class,
                AddQueuedCookiesToResponse::class,
                EncryptCookies::class,
                FrameHeadersMiddleware::class,
                StartSession::class,
                ShareErrorsFromSession::class,
                SubstituteBindings::class,
                DispatchServingFilamentEvent::class,
            ])
                ->brandLogo(new HtmlString(''))
                ->authMiddleware([
                     VerifyShopifyCopy::class,
                     Billable::class,
                ]);
        } else {
            LanguageSwitch::configureUsing(function (LanguageSwitch $switch) {
                $switch
                    ->locales(config('app.locales')); // also accepts a closure
            });
        }

        return $panel
            ->middleware([
                DisableBladeIconComponents::class,
                AddQueuedCookiesToResponse::class,
                EncryptCookies::class,
                FrameHeadersMiddleware::class,
                StartSession::class,
                ShareErrorsFromSession::class,
                SubstituteBindings::class,
                VerifyCsrfToken::class,
                DispatchServingFilamentEvent::class,
            ])
            ->brandLogo('/storage/img/commentier.svg')
            ->brandLogoHeight('2rem')
            ->authMiddleware([
                Authenticate::class,
            ])->viteTheme('resources/css/filament/admin/theme.css');


    }

    public function boot(): void
    {
        if (CheckIfShopifyRequest::execute()) {
            FilamentView::registerRenderHook(
                'panels::head.start',
                function (): string {
                    $shopDomain = request()->get('shop');
                    $host = request()->get('host');
                    $apiKey = Util::getShopifyConfig('api_key', $shopDomain ?? Auth::user()?->name);

                    return <<<HTML
                        <meta name="shopify-api-key" content="{$apiKey}" />
                        <script src="https://cdn.shopify.com/shopifycloud/app-bridge.js"></script>
                        <style>
                        .fi-user-menu {
                            display: none !important;
                        }
                        </style>
                    HTML;
                }
            );
        }

        if (AppSettings::getCustomNotificationStatus()) {
            FilamentView::registerRenderHook(
                'panels::head.start',
                function (): string {
                    return <<<HTML
                        <script>
                        document.addEventListener('customNotification', function (event) {
                           shopify.toast.show(event.detail.title);
                        });
                        </script>
                    HTML;
                }
            );
        }

        FilamentView::registerRenderHook(
            'panels::body.end',
            function (): string {
                if(!Auth::user()) {
                    return '';
                }

                $helpDeskUrl = route('filament.admin.resources.tickets.index');
                return <<<HTML
            <style>
                .helpdesk-button {
                    position: fixed;
                    bottom: 24px;
                    right: 24px;
                    width: 56px;
                    height: 56px;
                    background-color: #b6e8ac;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
                    transition: background-color 0.2s ease;
                    z-index: 50;
                    text-decoration: none;
                    cursor: pointer;
                }
                
                .helpdesk-button:hover {
                    background-color: #2563eb;
                }
                
                .helpdesk-button svg {
                    width: 24px;
                    height: 24px;
                    color: white;
                }
            </style>
            
            <a href="{$helpDeskUrl}" wire:navigate class="helpdesk-button" title="View Support Tickets">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                </svg>
            </a>
        HTML;
            }
        );
    }
}
