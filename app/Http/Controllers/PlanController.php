<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class PlanController extends Controller
{
    private $plans = [
        'starter' => ['name' => 'Starter', 'price' => 1900], // in cents
        'growth' => ['name' => 'Growth', 'price' => 4900],
        'pro' => ['name' => 'Pro', 'price' => 9900],
    ];

    public function show($plan)
    {
        if (!isset($this->plans[$plan])) abort(404);
        return view('plans.show', ['plan' => $this->plans[$plan], 'key' => $plan]);
    }

    public function checkout(Request $request, $plan)
    {
        if (!isset($this->plans[$plan])) abort(404);
        return view('plans.checkout', [
            'plan' => $this->plans[$plan],
            'key' => $plan,
            'stripeKey' => config('services.stripe.key'),
        ]);
    }

    public function processCheckout(Request $request, $plan)
    {


        return redirect()->route('plans.show', $plan)->with('success', 'Payment successful!');
    }
}
