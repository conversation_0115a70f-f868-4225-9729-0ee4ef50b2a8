<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Session;
use Laravel\Socialite\Facades\Socialite;

class JudgeMeController extends Controller
{
    public function redirectToProvider()
    {
        $scopes = ['read_products', 'write_products'];
        $state = bin2hex(random_bytes(16));
        Session::put('judgeme_oauth_state', $state);

        return Socialite::driver('judgeme')
            ->scopes($scopes)
            ->with(['state' => $state])
            ->redirect();
    }

    public function handleProviderCallback(Request $request)
    {
        $state = Session::pull('judgeme_oauth_state');
        if (strlen($state) > 0 && $state !== $request->input('state')) {
            abort(403, 'Invalid state parameter');
        }

        $code = $request->input('code');
        $response = Http::post('https://judge.me/oauth/token', [
            'client_id' => config('services.judgeme.client_id'),
            'client_secret' => config('services.judgeme.client_secret'),
            'code' => $code,
            'redirect_uri' => config('services.judgeme.redirect'),
            'grant_type' => 'authorization_code',
        ]);

        if ($response->successful()) {
            $accessToken = $response->json('access_token');
            $user = Auth::user();
            $user->settings()->set('judgeme.access_token', $accessToken);
            return redirect()->route('dashboard')->with('status', 'Judge.me account connected successfully.');
        } else {
            return redirect()->route('dashboard')->withErrors('Failed to retrieve access token from Judge.me.');
        }
    }
}