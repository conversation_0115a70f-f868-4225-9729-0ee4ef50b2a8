<?php

namespace App\Http\Controllers;

use App\Models\User;
use Filament\Notifications\Notification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;

class PaytrController extends Controller
{
    public function success(Request $request)
    {
       $orderId = $request->get('merchant_oid');
       $status = $request->get('status');
       $amount = $request->get('payment_amount');

       if($status == 'success') {
           $userId = Cache::get($orderId);

           if(!$userId) {
               return response()->json(['error' => 'Order not found'], 404);
           }

           $user = User::find($userId);
              if ($user) {
                $amount = $amount / 100;
                $user->increment('balance', $amount);
                $user->save();
                Cache::forget($orderId);
              } else {
                return response()->json(['error' => 'User not found'], 404);
              }
       }

        die('OK');
    }

    public function fail(Request $request)
    {
        Notification::make()
            ->title(__('messages.notifications.payment.failed'))
            ->body(__('messages.notifications.payment.failed_body'))
            ->danger()
            ->send();

        return redirect()->route('filament.admin.pages.add-balance')
            ->withErrors(['payment' => 'Payment failed.']);
    }

    public function generateToken(Request $request)
    {
        $request->validate([
            'user_ip' => 'required',
            'merchant_oid' => 'required|string',
            'email' => 'required|email',
            'payment_amount' => 'required|numeric',
            'payment_type' => 'required',
            'currency' => 'required|string',
            'test_mode' => 'required|boolean',
            'non_3d' => 'required|boolean'
        ]);

        $merchant_id = config('pay.paytr.merchant_id');
        $merchant_key = config('pay.paytr.merchant_key');
        $merchant_salt = config('pay.paytr.merchant_salt');
        Cache::put($request->merchant_oid, Auth::user()->id, 60 * 5);

        $hash_str = $merchant_id .
            $request->user_ip .
            $request->merchant_oid .
            $request->email .
            $request->payment_amount .
            $request->payment_type .
            $request->installment_count ?? 0 .
            $request->currency .
            $request->test_mode .
            $request->non_3d;

        $paytr_token = base64_encode(hash_hmac('sha256', $hash_str . $merchant_salt, $merchant_key, true));

        return response()->json(['token' => $paytr_token]);
    }
}
