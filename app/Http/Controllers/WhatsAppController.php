<?php

namespace App\Http\Controllers;

use App\Services\Messengers\MessengerEngine;
use Illuminate\Http\Request;

class WhatsAppController extends Controller
{
    public function messageCallback(Request $request)
    {
        // Handle incoming messages from WhatsApp
        MessengerEngine::getActiveMessenger()->parseMessageFromRequest($request);

        return response()->json(['status' => 'success']);
    }

    public function statusCallback()
    {
        // Handle status callback from WhatsApp
        $data = request()->all();

        die($data['hub_challenge']);

        // Process the status update
        // For example, you can update the message status in the database
        // $message = Message::find($data['message_id']);
        // $message->update(['status' => $data['status']]);

        return response()->json(['status' => 'success']);
    }
}
