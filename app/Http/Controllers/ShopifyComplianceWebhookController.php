<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Jobs\ShopifyCompliance\HandleShopRedactJob;
use App\Jobs\ShopifyCompliance\HandleCustomerRedactJob;
use App\Jobs\ShopifyCompliance\HandleCustomerDataRequestJob;

class ShopifyComplianceWebhookController extends Controller
{
    public function handleDataRequest(Request $request)
    {
        $payload = $request->json()->all();
        HandleCustomerDataRequestJob::dispatch($payload);
       
    }

    public function handleRedactRequest(Request $request)
    {
        $payload = $request->json()->all();
        HandleCustomerRedactJob::dispatch($payload);
    }

    public function handleShopRedactRequest(Request $request)
    {
        $payload    = $request->json()->all();
        Log::info('Redacting shop data started call');
        HandleShopRedactJob::dispatch($payload);
    }
}
