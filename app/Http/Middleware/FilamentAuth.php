<?php

namespace App\Http\Middleware;

use App\Services\Shops\Shopify\CheckIfShopifyRequest;
use Closure;
use Filament\Http\Middleware\Authenticate;
use Illuminate\Http\Request;
use Osiset\ShopifyApp\Http\Middleware\VerifyShopify;
use Symfony\Component\HttpFoundation\Response;

class FilamentAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if(CheckIfShopifyRequest::execute()) {
            return app(VerifyShopify::class)->handle($request, $next);
        }

        return app(Authenticate::class)->handle($request, $next);
    }
}
