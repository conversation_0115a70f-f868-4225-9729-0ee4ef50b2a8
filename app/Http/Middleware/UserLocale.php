<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class UserLocale
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if(auth()->user()) {
            $locale = auth()->user()->locale;
            $availableLocales = config('app.available_locales', []);
            if ($locale && in_array($locale, $availableLocales)) {
                app()->setLocale($locale);
            } else {
                app()->setLocale(config('app.fallback_locale', 'en'));
            }
        } else {
            app()->setLocale(config('app.fallback_locale', 'en'));
        }
        return $next($request);
    }
}
