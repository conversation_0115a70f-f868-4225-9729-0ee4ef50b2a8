<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class DisallowBots
{
    public function handle(Request $request, Closure $next): Response
    {
        if ($request->is('robots.txt') && config('seo.disallow_bots')) {
            return response("User-agent: *\nDisallow: /", 200)
                ->header('Content-Type', 'text/plain');
        }

        return $next($request);
    }
}
