<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class VerifyShopifyWebhook
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Retrieve the HMAC header and raw request body
        $hmacHeader = $request->header('X-Shopify-Hmac-Sha256');
        $rawBody = $request->getContent();

        // Calculate the HMAC using the shared secret
        $calculatedHmac = base64_encode(hash_hmac('sha256', $rawBody, config('shopify-app.api_secret'), true));

        // Compare the calculated HMAC with the received HMAC
        if (!hash_equals($hmacHeader, $calculatedHmac)) {
            Log::error('Shopify webhook HMAC validation failed.');
            return response('Unauthorized', 401);
        }

        return $next($request);
    }
}
