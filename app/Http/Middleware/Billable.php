<?php

namespace App\Http\Middleware;

use Closure;
use App\Models\User;
use Osiset\ShopifyApp\Util;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use Symfony\Component\HttpFoundation\Response;

class Billable
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (Util::getShopifyConfig('billing_enabled') === false) {
            return $next($request);
        }

        if (auth()->user() instanceof User && auth()->user()->isSuperAdmin()) {
            // Skip billing check for super admin users
            return $next($request);
        }

        if (Util::getShopifyConfig('billing_enabled') === true) {
            $shop  = auth()->user();

            if (!$shop->plan && !$shop->isFreemium() && !$shop->isGrandfathered()) {
                return Redirect::route(
                    Util::getShopifyConfig('route_names.billing'),
                    array_merge($request->input(), [
                        'shop' => $shop->getDomain()->toNative(),
                        'host' => $request->host,
                    ])
                );
            }
        }
        return $next($request);
    }
}
