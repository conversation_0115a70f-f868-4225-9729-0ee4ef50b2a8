<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use App\Observers\UserObserver;
use Glorand\Model\Settings\Traits\HasSettingsTable;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Osiset\ShopifyApp\Contracts\Objects\Values\AccessToken as AccessTokenValue;
use Osiset\ShopifyApp\Contracts\ShopModel as IShopModel;
use Osiset\ShopifyApp\Objects\Values\AccessToken;
use Osiset\ShopifyApp\Traits\ShopModel;

#[ObservedBy([UserObserver::class])]
class User extends Authenticatable implements IShopModel
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, ShopModel, HasSettingsTable, SoftDeletes;

    protected $guarded = ['is_super_admin'];
    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'plan_id',
        'store_type',
        'locale'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'balance' => 'float',
        ];
    }

    public function isSuperAdmin(): bool
    {
        return $this->is_super_admin;
    }
    
    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function items()
    {
        return $this->hasMany(Item::class);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function conversations()
    {
        return $this->hasMany(Conversation::class);
    }

    public function stats()
    {
        return $this->hasMany(Stats::class);
    }

    public function getAccessToken(): AccessTokenValue
    {
        if($this->settings()->has('shopify.offline_access_token')) {
            return AccessToken::fromNative($this->settings()->get('shopify.offline_access_token'));
        }

        return AccessToken::fromNative($this->password);
    }

    public function templates()
    {
        return $this->hasMany(MessageTemplates::class);
    }

    public function storeName()
    {
        return collect(explode('.', $this->name))->first();
    }

    public function hasPermissionTo($permission)
    {
        return in_array($permission, $this->plan?->permissions ?? []);
    }

    public function getSetting($key)
    {
        return $this->settings()->get($key);
    }

    public function setSetting($key, $value)
    {
        $this->settings()->set($key, $value);
    }

    public function getBalanceAmount(): float
    {
        return $this->attributes['balance'] ?? 0.0;
    }

}
