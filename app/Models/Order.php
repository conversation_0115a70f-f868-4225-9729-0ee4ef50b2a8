<?php

namespace App\Models;

use App\Enums\PaymentStatus;
use App\Enums\Settings;
use App\Models\Scopes\UserScope;
use App\Observers\OrderObserver;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Attributes\ScopedBy;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Date;

#[ScopedBy([UserScope::class])]
#[ObservedBy([OrderObserver::class])]
class Order extends Model
{
    protected $guarded = [];

    protected $appends = [
        'message_date_at',
        'remaining_days',
        'formatted_order_number',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function conversation()
    {
        return $this->hasMany(Conversation::class);
    }

    public function items()
    {
        return $this->hasMany(Item::class);
    }

    public function messages()
    {
        return $this->hasMany(Message::class);
    }

    public function cleanOrderNumber()
    {
        return collect(explode('/', $this->order_number))->last();
    }

    public function getMessageDateAtAttribute()
    {
        $orderCreatedAtDate = Date::make($this->order_created_at);
        $sendMessageDelayOfUser = $this->user->getSetting(Settings::MESSAGE_SEND_AFTER_DAYS->value);
        return $orderCreatedAtDate->addDays(intval($sendMessageDelayOfUser));
    }

    public function getRemainingDaysAttribute()
    {
        $messagingDate = Date::make($this->message_date_at);
        $currentDate = now();
        $diff = max($currentDate->diffInDays($messagingDate), -1);
        return intval($diff);
    }

    public function getFormattedOrderNumberAttribute(): string
    {
        return '#' . $this->cleanOrderNumber();
    }

    public function paymentStatusEnum(): ?PaymentStatus
    {
        return PaymentStatus::tryFromValue($this->status);
    }

    public function statusLabel(): string
    {
        return $this->paymentStatusEnum()?->label() ?? $this->status;
    }

    public function statusColor(): string
    {
        return $this->paymentStatusEnum()?->color() ?? 'bg-gray-50 text-gray-700 border border-gray-200';
    }
}
