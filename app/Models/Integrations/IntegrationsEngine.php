<?php

namespace App\Models\Integrations;

use App\Models\Review;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class IntegrationsEngine
{
    /**
     * @param Review $review
     * @param $code
     * @return void
     * @throws \Exception
     */
    public function executeByCode(Review $review, $code): void
    {
        $integration = $this->getByCode($code);
        if (!$integration) {
            throw new \Exception("Integration with code {$code} not found.");
        }
        $integration->publish($review);
    }

    /**
     * @param Review $review
     * @return void
     * @throws \Exception
     */
    public function executeAll(Review $review): void
    {
        $integrations = $this->getAll();
        $user = $review->user;
        if (!$user) {
            throw new \Exception("User not found for the review.");
        }

        if (empty($integrations)) {
            throw new \Exception("No integrations found.");
        }

        /** @var IntegrationAbstract $class */
        foreach ($integrations as $class) {
            if (class_exists($class)) {
                try {
                    /** @var IntegrationAbstract $integration */
                    $integration = app($class, ['user' => $user]);
                    $integration->publish($review);
                } catch (\Exception $e) {
                    Log::error("Failed to publish review via integration {$class}: " . $e->getMessage() . ' - Trace: ' . $e->getTraceAsString());
                }
            }
        }
    }

    public function getAll(): array
    {
        return [
            JudgeMe::class,
        ];
    }

    public function getCodes(): array
    {
        return array_map(function ($class) {
            return app($class)->getCode();
        }, $this->getAll());
    }

    public function getByCode(string $code): ?IntegrationAbstract
    {
        foreach ($this->getAll() as $class) {
            if (app($class)->getCode() === $code) {
                return app($class);
            }
        }
        return null;
    }

    public function getAllActivesCodesByUser(User $user)
    {
        $activeIntegrations = [];
        foreach ($this->getAll() as $class) {
            $integration = app($class, ['user' => $user]);
            if ($integration->isActive()) {
                $activeIntegrations[] = $integration->getCode();
            }
        }

        return $activeIntegrations;
    }
}