<?php

namespace App\Models\Integrations;

use App\Models\Review;
use Illuminate\Support\Facades\Log;

class Judge<PERSON><PERSON> extends IntegrationAbstract
{
    public function getCode(): string
    {
        return 'judgeme';
    }

    public function getName(): string
    {
        return 'Judge.me';
    }

    public function getSvg(): string
    {
        return '<svg viewBox="0 0 40 40" id="svg11671298782"><path d="M 2.26 0 C 1.012 0 0 1.012 0 2.26 L 0 37.383 C 0 38.631 1.012 39.643 2.26 39.643 L 37.383 39.643 C 38.631 39.643 39.643 38.631 39.643 37.383 L 39.643 22.534 C 39.643 21.819 38.772 21.47 38.278 21.985 L 30.448 30.155 C 27.103 33.646 23.611 35.382 19.973 35.363 C 16.363 35.372 12.935 33.754 9.688 30.507 C 6.241 27.059 4.424 23.319 4.239 19.285 C 4.094 15.56 5.548 11.968 8.601 8.511 C 8.988 8.073 9.653 8.067 10.065 8.48 L 16.94 15.354 C 17.349 15.763 17.353 16.429 16.996 16.886 C 15.334 19.009 15.271 20.838 16.807 22.374 C 18.359 23.925 20.09 23.703 22.002 21.709 L 39.422 3.53 C 39.564 3.382 39.643 3.186 39.643 2.981 L 39.643 2.26 C 39.643 1.012 38.631 0 37.383 0 Z" fill="var(--token-c99ac272-cff0-4e5b-b581-d6b715f9c3af, rgb(62, 178, 162))"></path></svg>';
    }

    public function getSettings(): array
    {
        return [
            'public_api_token' => 'Public API token',
            'private_api_token' => 'Private API token',
            ];
    }

    public function publishReview(Review $review): bool
    {
        $data = [
            'shop_domain' => $review->user->name,
            'platform' => 'shopify',
            'name' => $review->order->customer_name,
            'email' => $review->order->customer_email,
            'rating' => $review->rating,
            'body' => $review->review_description,
            'id' => $review->item->cleanProductId()
        ];

        $response = $this->post('https://api.judge.me/api/v1/reviews', $data, [
            'Authorization' => 'Bearer ' . $this->getSetting('private_api_token'),
            'Content-Type' => 'application/json',
        ]);

        if ($response->successful()) {
            $review->update(['review_status' => 'published']);
            $review->update(['is_published' => 1]);
            return true;
        } else {
            $review->update(['review_status' => 'failed']);
            Log::error( 'Review ID ' . $review->id . ': Judge.me API error: ' . $response->body());
            return false;
        }
    }
}