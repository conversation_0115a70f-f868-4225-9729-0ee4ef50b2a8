<?php

namespace App\Models\Integrations;

use App\Models\Review;
use App\Models\User;

abstract class IntegrationAbstract
{
    public function __construct(protected ?User $user)
    {
        if((!$user || !$user->id) && auth()->user()) {
            $this->user = auth()->user();
        }
    }

    abstract public function getCode(): string;
    abstract public function getName(): string;
    abstract public function getSvg(): string;
    abstract public function getSettings(): array;
    abstract protected function publishReview(Review $review): bool;

    public function setUser(User $user): self
    {
        $this->user = $user;
        return $this;
    }

    public function publish(Review $review)
    {
        if (!$this->user) {
            $this->user = $review->user;
        }

        if (!$this->user) {
            return false;
        }

        if (!$this->isActive()) {
            return false;
        }

        if (!$this->isInstalled()) {
            return false;
        }

        if($this->publishReview($review)) {
            $review->integration_codes = array_unique(array_merge($review->integration_codes ?? [], [$this->getCode()]));
            $review->save();
            return true;
        }

        return false;
    }

    protected function getSetting($key)
    {
        return $this->user->settings()->get('integrations.' . $this->getCode() . '.settings.' . $key);
    }

    private function setSetting($key, $value): self
    {
        $this->user->settings()->set('integrations.' . $this->getCode() . '.settings.' . $key, $value);
        $this->user->save();
        return $this;
    }

    public function isActive(): bool
    {
        return boolval($this->getSetting('active'));
    }

    public function setActive($status = true): self
    {
        $this->setSetting('active', $status);
        return $this;
    }
    public function isInstalled(): bool
    {
        return boolval($this->getSetting('installed'));
    }

    public function setInstalled($status = true): self
    {
        $this->setSetting('installed', $status);
        return $this;
    }

    public function setSettings(array $settings): self
    {
        foreach ($settings as $key => $value) {
            $this->setSetting($key, $value);
        }
        return $this;
    }

    public function getSettingsValues(): array
    {
        return $this->user->settings()->get('integrations.' . $this->getCode() . '.settings') ?? [];
    }

    public function remove(): void
    {
        $this->setInstalled(false);
        $this->setActive(false);
        $this->user->settings()->set('integrations.' . $this->getCode(), null);
        $this->user->save();
    }

    protected function post(string $url, array $data, array $headers = []): \Illuminate\Http\Client\Response
    {
        return \Illuminate\Support\Facades\Http::withHeaders($headers)->post($url, $data);
    }
}