<?php

namespace App\Models;

use App\Enums\SenderTypes;
use App\Models\Scopes\UserScope;
use Illuminate\Database\Eloquent\Attributes\ScopedBy;
use Illuminate\Database\Eloquent\Model;

#[ScopedBy([UserScope::class])]
class MessageTemplates extends Model
{
    protected $guarded = [];

    protected Order $order;
    public Message $message;

    public function setOrder(Order $order): static
    {
        $this->order = $order;
        return $this;
    }

    public function fillTemplate(Model $model): static
    {
        preg_match_all('/{{(.*?)}}/', $this->template, $matches);
        foreach ($matches[1] as $match) {
            $attribute = trim($match);
            if (in_array($attribute, array_keys($model->attributesToArray()))) {
                $attributeVal = $model->{$attribute};
                if($attribute == 'order_number') {
                    $attributeVal = $model->cleanOrderNumber();
                }
                $this->template = str_replace("{{{$match}}}", $attributeVal, $this->template);
            }
        }

        return $this;
    }

    public function generateMessage(Model $model): Message
    {
        $this->fillTemplate($model);

        $this->message = Message::create([
            'user_id'      => $this->user_id,
            'order_id'     => $this->order->id,
            'item_id'      => $model instanceof \App\Models\Item ? $model->id : null,
            'sender'       => SenderTypes::SYSTEM,
            'type'         => $this->type,
            'phone_number' => $this->order->phone_number,
            'message'      => $this->template,
        ]);

        return $this->message;
    }

    public function addItems(): void
    {
        $itemsToAsk = "\n";
        $items = $this->order->items->where('has_review', false)->values();

        foreach ($items as $index => $item) {
            $itemsToAsk .= ($index + 1) . " - " . $item->name . "\n";
        }

        if (isset($this->message)) {
            $this->message->message .= $itemsToAsk;
            $this->message->save();
        }
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function save(array $options = [])
    {
        if (!isset($this->user_id)) {
            $this->user_id = auth()->id();
        }

        return parent::save($options);
    }
}
