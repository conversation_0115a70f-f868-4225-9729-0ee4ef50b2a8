<?php

namespace App\Models;

use App\Models\Scopes\UserScope;
use App\Observers\ReviewObserver;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Attributes\ScopedBy;
use Illuminate\Database\Eloquent\Model;

#[ScopedBy([UserScope::class])]
#[ObservedBy([ReviewObserver::class])]
class Review extends Model
{
    protected $guarded = [];

    protected $casts = [
        'review_status' => 'boolean',
        'integration_codes' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function item()
    {
        return $this->belongsTo(Item::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function conversation()
    {
        return $this->belongsTo(Conversation::class);
    }
}
