<?php

namespace App\Models;

use App\Enums\MessageStatus;
use App\Enums\Settings;
use App\Models\Scopes\UserScope;
use App\Observers\MessageObserver;
use App\Services\Messengers\MessengerEngine;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Attributes\Scope;
use Illuminate\Database\Eloquent\Attributes\ScopedBy;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Date;

#[ObservedBy([MessageObserver::class])]
#[ScopedBy([UserScope::class])]
class Message extends Model
{
    protected $guarded = ['id', 'created_at', 'updated_at'];

    protected $casts = [
        'status' => MessageStatus::class,
        'price' => 'float',
    ];

    public function send()
    {
        MessengerEngine::getActiveMessenger()->send($this);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function item()
    {
        return $this->belongsTo(Item::class);
    }

    public function getZonedTime()
    {
        $timeZone = $this->order->user->getSetting(Settings::TIMEZONE->value);

        if($timeZone) {
            return Date::make($this->created_at)
                ->timezone($timeZone)
                ->format('d.m.Y H:i');
        }

        return Date::make($this->created_at)
            ->timezone('Europe/Istanbul')
            ->format('d.m.Y H:i');
    }

    /**
     * Scope a query to only include popular users.
     */
    #[Scope]
    protected function success(Builder $query): void
    {
        $query->where('status', MessageStatus::SENT->value);
    }
}
