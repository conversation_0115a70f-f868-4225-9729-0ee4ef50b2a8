<?php

namespace App\Models;

use App\Models\Scopes\UserScope;
use Illuminate\Database\Eloquent\Attributes\ScopedBy;
use Illuminate\Database\Eloquent\Model;

#[ScopedBy([UserScope::class])]
class Stats extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'year',
        'month',
        'total_messages',
        'total_reviews',
        'total_items',
        'total_orders',
        'remaining_reviews',
        'average_rating',
        'total_cost_current_month',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'average_rating'           => 'double',
        'total_cost_current_month' => 'double',
    ];

    /**
     * The model's default values for attributes.
     *
     * @var array
     */
    protected $attributes = [
        'total_messages'           => 0,
        'total_reviews'            => 0,
        'total_items'              => 0,
        'total_orders'             => 0,
        'remaining_reviews'        => 0,
        'average_rating'           => 0,
        'total_cost_current_month' => 0,
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public static function getStats()
    {
        $userId = auth()->id();
        $check = self::where('user_id', $userId)
            ->where('year', date('Y'))
            ->where('month', date('m'))
            ->first();

        if ($check) {
            return $check;
        } else {
            $stats = new self();
            $stats->user_id = $userId;
            $stats->year = date('Y');
            $stats->month = date('m');
            $stats->save();
            return $stats;
        }
    }
}