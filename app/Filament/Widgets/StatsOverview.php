<?php
namespace App\Filament\Widgets;

use App\Models\Stats;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Card;

class StatsOverview extends BaseWidget
{
    protected function getCards(): array
    {
        $latestStats = Stats::getStats();
        $comingReviews = $latestStats->total_items - $latestStats->total_reviews;
        return [

            Card::make(__('Total Items'), $latestStats?->total_reviews ?? 0)
                ->description(__('Item processed this month'))
                ->descriptionIcon('heroicon-m-shopping-cart')
                ->color('info'),

            Card::make(__('Total Reviews'), $latestStats?->done_reviews ?? 0)
                ->description(__('Reviews collected this month'))
                ->descriptionIcon('heroicon-m-star')
                ->color('success'),

           Card::make(__('Remaining reviews'), $latestStats?->remaining_reviews ?? 0)
                ->description(__('Before next quota reset'))
                ->descriptionIcon('heroicon-m-star')
                ->color('warning'),

           Card::make(__('Total Messages'), $latestStats?->total_messages ?? 0)
                ->description(__('Messages this month'))
                ->descriptionIcon('heroicon-m-chat-bubble-bottom-center-text')
                ->color('primary'),

            Card::make(__('Average Rating'), number_format($latestStats?->average_rating ?? 0, 2))
                ->description(__('Current average review rating'))
                ->descriptionIcon('heroicon-m-star')
                ->color('success'),

            Card::make(__('Coming Reviews'), $comingReviews)
                ->description(__('Reviews to be submitted'))
                ->descriptionIcon('heroicon-m-star')
                ->color('warning'),
        ];
    }
}

