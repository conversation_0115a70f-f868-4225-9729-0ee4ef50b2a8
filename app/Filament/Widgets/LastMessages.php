<?php

namespace App\Filament\Widgets;

use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Contracts\Support\Htmlable;

class LastMessages extends BaseWidget
{
    public function table(Table $table): Table
    {
        return $table
            ->query(
                // Assuming you have a model for messages, e.g., Message::query()
                \App\Models\Message::query()
                    ->with(['user', 'item', 'order'])
                    ->latest()
                    ->take(10) // Limit to the last 10 messages
            )
            ->columns([
                Tables\Columns\TextColumn::make('order.formatted_order_number')
                    ->label('Order Number')
                    ->translateLabel()
                    ->searchable(),
                Tables\Columns\TextColumn::make('sender')
                    ->label('Sender')
                    ->translateLabel()
                    ->formatStateUsing(fn ($state, $record) => $state === 'system' ? 'Commentier' : $record?->order?->customer_name)
                    ->searchable(),
                Tables\Columns\TextColumn::make('message')
                    ->label('Message Content')
                    ->translateLabel()
                    ->limit(50), // Limit the content length for display
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Sent At')
                    ->translateLabel()
                    ->dateTime()
            ])->recordUrl(fn ($record = null) => route('filament.admin.resources.orders.view', [
                'record' => $record?->order?->id ?? 0,
            ]));
    }

    protected int | string | array $columnSpan = 2;

    protected function getTableHeading(): string | Htmlable | null
    {
        return __('Last Messages');
    }
}
