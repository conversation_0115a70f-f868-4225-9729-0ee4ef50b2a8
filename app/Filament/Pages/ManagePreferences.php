<?php

namespace App\Filament\Pages;

use App\Enums\Permissions\Message;
use App\Enums\Permissions\Review;
use App\Enums\Settings;
use App\Services\AppSettings;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\TimePicker;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Glorand\Model\Settings\Contracts\SettingsManagerContract;
use Illuminate\Contracts\Support\Htmlable;
use Quadrubo\FilamentModelSettings\Pages\Contracts\HasModelSettings;
use Quadrubo\FilamentModelSettings\Pages\ModelSettingsPage;

class ManagePreferences extends ModelSettingsPage implements HasModelSettings
{
    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';

    protected static ?string $navigationLabel = 'Settings';

    protected static ?int $navigationSort = 99;

    public static function getSettingRecord()
    {
        return auth()->user();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                // Tabs container with full width
                Tabs::make(__('General Settings'))
                    ->columnSpan('full') // Make the tabs container full width
                    ->tabs([
                        Tabs\Tab::make(__('Message Settings'))
                            ->schema([
                                TextInput::make(Settings::MESSAGE_SEND_AFTER_DAYS->value)
                                    ->label(__('Days After Order to Send Messages'))
                                    ->numeric()
                                    ->required()
                                    ->default(0)
                                    ->minValue(0)
                                    ->maxValue(30)
                                    ->helperText(__('Number of days after the order to send messages.')),
                                Toggle::make(Settings::ASK_FOR_COMMENT->value)
                                    ->label(__('Ask for Comment After Rating'))
                                    ->translateLabel()
                                    ->default(0)
                                    ->helperText(__('Whether to ask customers for comments after they rate the product.')),
                            ])->columns(2),
                        Tabs\Tab::make(__('Auto Message & Review Settings'))
                            ->schema([
                                Toggle::make(Settings::AUTO_MESSAGE_SEND->value)
                                    ->label(__('Enable Auto Message Send'))
                                    ->hint(!AppSettings::permissionCheck(Message::AUTO_SEND->value) ? __('You do not have permission to use this feature.') : null)
                                    ->default(0)
                                    ->helperText(__('Automatically send messages after an order is placed.'))
                                    ->reactive()
                                    ->disabled(!AppSettings::permissionCheck(Message::AUTO_SEND->value)),
                                Group::make()
                                    ->columnSpan(2)
                                    ->schema([
                                        TimePicker::make(Settings::AUTO_MESSAGE_SEND_TIME->value)
                                            ->label(__('Auto Message Send Time'))
                                            ->hint(!AppSettings::permissionCheck(Message::AUTO_SEND->value) ? __('You do not have permission to use this feature.') : null)
                                            ->helperText(__('Set the time of day for sending automated messages.'))
                                            ->required()
                                            ->seconds(false)
                                            ->disabled(!AppSettings::permissionCheck(Message::AUTO_SEND->value)),
                                        Select::make(Settings::TIMEZONE->value)
                                            ->options(config('timezones'))
                                            ->required()
                                            ->label(__('Select Timezone'))
                                            ->disabled(!AppSettings::permissionCheck(Message::AUTO_SEND->value)),
                                    ])->reactive()->hidden(fn (callable $get) => !$get(Settings::AUTO_MESSAGE_SEND->value))->columnSpan(2),
                                Toggle::make(Settings::AUTO_REVIEW_PUBLISH->value)
                                    ->label(__('Enable Auto Review Publish'))
                                    ->hint(!AppSettings::permissionCheck(Review::AUTO_PUBLISH->value) ? __('You do not have permission to use this feature.') : null)
                                    ->default(0)
                                    ->reactive()
                                    ->helperText(__('Automatically publish reviews after order is placed.'))
                                    ->disabled(!AppSettings::permissionCheck(Review::AUTO_PUBLISH->value)),
                                //Auto publish conditions
                                Group::make()
                                    ->schema([
                                        //Rate value condition to auto publish review
                                        Select::make(Settings::MINIMUM_RATE_FOR_AUTO_PUBLISH->value)
                                            ->label(__('Rate Value for Auto Publish'))
                                            ->options([
                                                1 => __('1 Star'),
                                                2 => __('2 Stars'),
                                                3 => __('3 Stars'),
                                                4 => __('4 Stars'),
                                                5 => __('5 Stars'),
                                            ])
                                            ->required()
                                            ->default(1)
                                            ->columnSpan(1)
                                            ->hint(__('Select the minimum rating value to auto publish the review.'))
                                            ->reactive()->hidden(fn (callable $get) => !$get(Settings::AUTO_REVIEW_PUBLISH->value))
                                            ->disabled(!AppSettings::permissionCheck(Review::AUTO_PUBLISH->value)),
                                    ])
                            ]),
                        Tabs\Tab::make(__('Black Lists'))
                            ->schema([
                                //Black listed phone numbers
                                Repeater::make(Settings::BLACK_LISTED_PHONE_NUMBERS->value)
                                    ->label(__('Black Listed Phone Numbers'))
                                    ->reorderable(false)
                                    ->grid(2)
                                    ->columnSpan('full')
                                    ->hint(__('List of phone numbers that should not receive messages.'))
                                    ->schema([
                                        TextInput::make('phone_number')
                                            ->label(__('Phone Number'))
                                            ->required()
                                            ->tel()
                                            ->placeholder('+1234567890'),
                                    ])->defaultItems(0),

                                //Black listed product SKUs
                                Repeater::make(Settings::BLACK_LISTED_PRODUCT_SKUS->value)
                                    ->label(__('Black Listed Product SKUs'))
                                    ->reorderable(false)
                                    ->grid(2)
                                    ->columnSpan('full')
                                    ->hint(__('List of product SKUs that should not receive messages.'))
                                    ->schema([
                                        TextInput::make('sku')
                                            ->label(__('Product SKU'))
                                            ->required()
                                            ->placeholder('SKU12345'),
                                    ])->defaultItems(0)
                            ])->columns(2),
                    ])
            ]);
    }

    protected function handleRecordUpdate(SettingsManagerContract $settings, array $data): SettingsManagerContract
    {
        foreach ($data as $key => $value) {
            $settings->set($key, $value);
        }

        return $settings;
    }

    public static function getNavigationLabel(): string
    {
        return __('Settings');
    }

    public function getTitle(): string|Htmlable
    {
        return __('Settings');
    }
}
