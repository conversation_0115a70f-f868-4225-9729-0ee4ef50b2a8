<?php

namespace App\Filament\Pages;

use App\Models\Integrations\IntegrationAbstract;
use App\Models\Integrations\IntegrationsEngine;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Collection;

class Integrations extends Page
{
    public static string $view = 'filament.pages.integrations';

    protected static ?string $navigationIcon = 'heroicon-o-link';

    protected static ?int $navigationSort = 98;


    public string $activeIntegrationCode = '';
    public array $settings = [];
    public array $settingsData = [];

    protected $listeners = ['confirmRemoveIntegration'];

    public function mount(): void
    {
        if ($this->activeIntegrationCode) {
            $integration = app(IntegrationsEngine::class)->getByCode($this->activeIntegrationCode);
            if ($integration) {
                $this->settingsData = $integration->getSettings();
                $this->settings = $integration->getSettingsValues();
            }
        }
    }

    public function installIntegration(string $code): void
    {
        $this->activeIntegrationCode = $code;
        $integration = app(IntegrationsEngine::class)->getByCode($this->activeIntegrationCode);
        if ($integration) {
            $this->settingsData = $integration->getSettings();
            $this->settings = $integration->getSettingsValues();
            $this->dispatch('open-modal', id: 'install-integration');
        }
    }

    public function saveIntegrationSettings(): void
    {
        $integration = app(IntegrationsEngine::class)->getByCode($this->activeIntegrationCode);
        if ($integration) {
            $integration->setSettings($this->settings)->setInstalled(true);

            $this->dispatch('close-modal', id: 'install-integration');

            Notification::make()
                ->title(__('messages.notifications.integrations.settings_saved'))
                ->success()
                ->send();
        }
    }

    public function toggleActivation(string $code): void
    {
        $integration = app(IntegrationsEngine::class)->getByCode($code);
        if ($integration) {
            $integration->setActive(!$integration->isActive());
            Notification::make()
                ->title(__('messages.notifications.integrations.toggled', ['name' => $integration->getName(), 'status' => $integration->isActive() ? __('activated') : __('deactivated')]))
                ->success()
                ->send();
        }
    }

    public function promptRemoveIntegration(string $code): void
    {
        $this->dispatch('confirm', [
            'title' => 'Are you sure?',
            'text' => "Do you really want to remove {$this->integrationEngine()->getByCode($code)?->getName()}?",
            'icon' => 'warning',
            'confirmButtonText' => 'Yes, remove it!',
            'cancelButtonText' => 'Cancel',
            'onConfirmed' => 'confirmRemoveIntegration',
            'data' => ['code' => $code],
        ]);
    }

    public function confirmRemoveIntegration(array $data): void
    {
        $code = $data['code'] ?? null;
        if ($code) {
            $integration = $this->integrationEngine()->getByCode($code);
            if ($integration) {
                // Perform the logic to remove the integration
                $integration->remove();
                $this->dispatch('close-modal', id: 'install-integration');
                Notification::make()
                    ->title(__('messages.notifications.integrations.removed', ['name' => $integration->getName()]))
                    ->success()
                    ->send();
            }
        }
    }

    protected function integrationEngine(): IntegrationsEngine
    {
        return app(IntegrationsEngine::class);
    }

    /**
     * @return Collection<int, IntegrationAbstract>
     */
    public function getIntegrations(): Collection
    {
        return collect($this->integrationEngine()->getAll())
            ->map(fn (string $class) => app($class));
    }

    public static function getNavigationLabel(): string
    {
        return __('Integrations');
    }

    public static function getLabel(): ?string
    {
        return __('Integrations');
    }

    public function getTitle(): string|Htmlable
    {
        return __('Integrations');
    }
}