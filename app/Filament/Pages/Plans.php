<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Osiset\ShopifyApp\Util;

class Plans extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-wallet';
    protected static string $view = 'filament.pages.plans';
    protected static ?int $navigationSort = 98;

    public $plans = [];
    public $currentPlanId;
    public string $billingPeriod = 'monthly';

    public function mount(): void
    {
        $this->loadPlans();
    }

    public function loadPlans(): void
    {
        // Load all plans initially
        $this->plans = DB::table('plans')
            ->where('billing_period', $this->billingPeriod)
            ->orderBy('price')
            ->get();
        $this->currentPlanId = Auth::user()?->plan_id;
    }

    public function setBillingPeriod(string $period): void
    {
        $this->billingPeriod = $period;
        
        // Reload plans with the new billing period
        $this->plans = DB::table('plans')
            ->where('billing_period', $period)
            ->orderBy('price')
            ->get();
    }

    public function upgrade(int $planId, $host)
    {
        $shop = Auth::user();
        $confirmationUrl = route(
            Util::getShopifyConfig('route_names.billing'),
            [
                'shop' => $shop->getDomain()->toNative(),
                'host' => $host,
                'plan' => $planId
            ]
        );

        return redirect()->away($confirmationUrl);
    }

    public static function getNavigationLabel(): string
    {
        return __('Plans');
    }

    public static function getLabel(): ?string
    {
        return __('Plans');
    }

    public function getTitle(): string|Htmlable
    {
        return __('Plans');
    }
}