<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ReviewResource\Pages;
use App\Filament\Resources\ReviewResource\RelationManagers;
use App\Jobs\PublishReview;
use App\Models\Review;
use App\Services\AppSettings;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;

class ReviewResource extends Resource
{
    protected static ?string $model = Review::class;

    protected static ?string $navigationIcon = 'heroicon-o-check-badge';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('user_id')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('order_id')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('item_id')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('conversation_id')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('rating')
                    ->required()
                    ->numeric()
                    ->default(0),
                Forms\Components\TextInput::make('review_title'),
                Forms\Components\Textarea::make('review_description')
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('review_status')
                    ->required(),
                Forms\Components\Toggle::make('is_published')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('order.order_number')
                    ->translateLabel(__('Order Number'))
                    ->formatStateUsing(fn($state) => "#" . collect(explode('/', $state))->last())
                    ->searchable(),
                Tables\Columns\TextColumn::make('item.name')
                    ->numeric()
                    ->label(__('Item Name'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('order.customer_name')
                    ->label('Customer Name')
                    ->translateLabel(__('Customer Name'))
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('rating')
                    ->formatStateUsing(fn($state) => str_repeat('⭐', $state))
                    ->translateLabel(__('Rating'))
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_published')
                    ->translateLabel(__('Published'))
                    ->boolean(),
                Tables\Columns\TextColumn::make('created_at')
                    ->translateLabel(__('Created At'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->translateLabel(__('Updated At'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('is_published')
                    ->label(__('Published'))
                    ->translateLabel()
                    ->options([
                        '1' => __('Yes'),
                        '0' => __('No'),
                    ])->attribute('is_published'),
                SelectFilter::make('rating')
                    ->label(__('Rating Filter'))
                    ->options([
                        1 => __('1 Star'),
                        2 => __('2 Stars'),
                        3 => __('3 Stars'),
                        4 => __('4 Stars'),
                        5 => __('5 Stars'),
                    ])->attribute('rating')
            ])->persistFiltersInSession()
            ->actions([
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('export')
                        ->label(__('Publish'))
                        ->action(function ($records) {
                            if(!AppSettings::permissionCheck(\App\Enums\Permissions\Review::PUBLISH->value)) {
                                Notification::make()
                                    ->title(__('messages.notifications.reviews.no_permission'))
                                    ->danger()
                                    ->send();
                                return;
                            }

                            foreach ($records as $record) {
                                PublishReview::dispatch($record);
                            }
                            Notification::make()
                                ->title(__('messages.notifications.reviews.will_be_published'))
                                ->success()
                                ->send();
                        })
                        ->icon('heroicon-o-paper-airplane'),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getGloballySearchableAttributes(): array
    {
        return ['item.name', 'order.order_number', 'order.customer_name'];
    }

    protected function shouldPersistTableFiltersInSession(): bool
    {
        return true;
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListReviews::route('/'),
            'view' => Pages\ViewReview::route('/{record}'),
        ];
    }

    public static function getNavigationLabel(): string
    {
        return __('Reviews');
    }

    public static function getLabel(): ?string
    {
        return __('Reviews');
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::where('is_published', false)->count() ?? null;
    }
}
