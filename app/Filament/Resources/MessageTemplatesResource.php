<?php

namespace App\Filament\Resources;

use App\Enums\MessageTypes;
use App\Filament\Resources\MessageTemplatesResource\Pages;
use App\Filament\Resources\MessageTemplatesResource\RelationManagers;
use App\Models\MessageTemplates;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\HtmlString;

class MessageTemplatesResource extends Resource
{
    protected static ?string $model = MessageTemplates::class;

    protected static ?string $navigationIcon = 'heroicon-o-chat-bubble-bottom-center-text';

    protected static ?int $navigationSort = 96;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required(),
                Forms\Components\Toggle::make('is_default')
                    ->label(__('Default'))
                    ->default(true),
                Forms\Components\Select::make('type')
                    ->options(collect(MessageTypes::cases())->mapWithKeys(fn($case) => [$case->value => __($case->name)])->toArray())
                    ->required(),
                Forms\Components\Placeholder::make('placeholders')
                    ->label('Available Template Variables')
                    ->content(new HtmlString("
        <div class='text-sm leading-relaxed'>
            <strong>From Order:</strong><br>
            {{order_number}}, {{status}}, {{total_amount}}, {{currency}}, {{payment_method}}, {{shipping_address}}, {{billing_address}}<br><br>

            <strong>From Order Item:</strong><br>
            {{name}}, {{sku}}, {{price}}, {{quantity}}, {{variant_id}}, {{product_id}}, {{img_url}}, {{url}}<br><br>

            <strong>From User:</strong><br>
            {{name}}, {{email}}, {{phone_number}}
        </div>
    "))
                    ->columnSpanFull()
                    ->helperText('You can use these variables inside the message template by wrapping them in double curly braces.'),
                Forms\Components\Textarea::make('template')
                    ->required()
                    ->rows(10)
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('type')
                    ->translateLabel()
                    ->formatStateUsing(fn($state) => __($state))
                    ->searchable(),
                Tables\Columns\BooleanColumn::make('is_default')
                    ->label(__('Default'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getNavigationLabel(): string
    {
        return __('Message Templates');
    }

    public static function getLabel(): ?string
    {
        return __('Message Templates');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMessageTemplates::route('/'),
            'create' => Pages\CreateMessageTemplates::route('/create'),
            'edit' => Pages\EditMessageTemplates::route('/{record}/edit'),
        ];
    }
}
