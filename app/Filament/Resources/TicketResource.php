<?php

namespace App\Filament\Resources;

use App\Enums\TicketPriorities;
use App\Enums\TicketTopics;
use App\Filament\Resources\TicketResource\Pages;
use App\Filament\Resources\TicketResource\RelationManagers;
use App\Models\Ticket;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class TicketResource extends Resource
{
    protected static ?string $model = Ticket::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static bool $shouldRegisterNavigation = false;

    public static function form(Form $form): Form
    {
        $cases = TicketTopics::cases();
        $priorities = TicketPriorities::cases();
        return $form
            ->schema([
                Forms\Components\Select::make('topic')
                    ->options(collect($cases)->mapWithKeys(fn($case) => [$case->value => $case->name]))
                    ->translateLabel(__('Topic'))
                    ->required(),
                Forms\Components\Select::make('priority')
                    ->translateLabel(__('Priority'))
                    ->options(collect($priorities)->mapWithKeys(fn($case) => [$case->value => $case->name]))
                    ->required(),
                Forms\Components\TextInput::make('subject')
                    ->translateLabel(__('Subject'))
                    ->required()
                    ->columnSpanFull(),
                Forms\Components\Textarea::make('message')
                    ->translateLabel(__('Message'))
                    ->required()
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label(__('Ticket ID'))
                    ->sortable()
                    ->formatStateUsing(fn (string $state): string => '#TKT-' . $state)
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('subject')
                    ->searchable(),
                Tables\Columns\TextColumn::make('topic')
                    ->searchable(),
                Tables\Columns\TextColumn::make('status')
                    ->formatStateUsing(fn ($state) => \App\Enums\TicketStatuses::labelFromValue($state))
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->defaultSort('created_at', 'desc')
            ->modifyQueryUsing(function ($query) {
                if (auth()->user()?->is_super_admin) {
                    return $query;
                }
                return $query->where('user_id', auth()->id());
            })
            ->filters([
                //
            ])
            ->actions(array_filter([
                Tables\Actions\ViewAction::make(),

                auth()->user()?->is_super_admin
                    ? Tables\Actions\Action::make('update_status')
                        ->label(__('Update Status'))
                        ->form([
                            Forms\Components\Select::make('status')
                                ->options(collect(\App\Enums\TicketStatuses::cases())->mapWithKeys(fn($case) => [$case->value => $case->name]))
                                ->required()
                                ->default(fn ($record) => $record?->status),
                        ])
                        ->action(function (array $data, Ticket $record) {
                            $record->status = $data['status'];
                            $record->save();

                            Notification::make()
                                ->title(__('messages.notifications.tickets.status_updated'))
                                ->body(__('messages.notifications.tickets.status_updated_body'))
                                ->icon('heroicon-o-check-circle')
                                ->success()
                                ->send();
                        })
                    : null,
            ]))
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getNavigationLabel(): string
    {
        return parent::getNavigationLabel(); // TODO: Change the autogenerated stub
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTickets::route('/'),
            'create' => Pages\CreateTicket::route('/create'),
            'view' => Pages\ViewTicket::route('/{record}'),
            'edit' => Pages\EditTicket::route('/{record}/edit'),
        ];
    }
}
