<?php

namespace App\Filament\Resources;

use App\Enums\Permissions\Message;
use App\Enums\Settings;
use App\Filament\Resources\OrderResource\Pages;
use App\Filament\Resources\OrderResource\RelationManagers;
use App\Jobs\SendMessageJob;
use App\Models\Order;
use App\Services\AppSettings;
use App\Services\User\GetBlackListedPhoneNumbers;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class OrderResource extends Resource
{
    protected static ?string $model = Order::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('user_id')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('order_number')
                    ->required(),
                Forms\Components\TextInput::make('status')
                    ->required(),
                Forms\Components\TextInput::make('total_amount')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('currency')
                    ->required(),
                Forms\Components\TextInput::make('payment_method'),
                Forms\Components\TextInput::make('shipping_address'),
                Forms\Components\TextInput::make('billing_address'),
                Forms\Components\DateTimePicker::make('order_created_at'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('order_number')
                    ->formatStateUsing(fn($state) => "#" . collect(explode('/', $state))->last())
                    ->searchable(),
                Tables\Columns\TextColumn::make('remaining_days')
                    ->label(__('Remaining Days'))
                    ->colors([
                        'warning' => fn($state) => $state >= 5,
                        'success' => fn($state) => $state < 5 && $state > 0,
                        'danger' => fn($state) => $state <= 0,
                    ])
                    ->formatStateUsing(fn($record, $state) => in_array($record->phone_number, GetBlackListedPhoneNumbers::execute()) ? __('Blacklisted') : ($state >= 0 ? $state . __(' days') : __('Expired')))
                    ->sortable(['order_created_at']),
                Tables\Columns\TextColumn::make('customer_name')
                     ->label(__('Customer Name'))
                     ->searchable(),
                Tables\Columns\TextColumn::make('customer_email')
                    ->label(__('Customer Email'))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->searchable(),
                Tables\Columns\TextColumn::make('phone_number')
                    ->label(__('Customer Phone'))
                    ->searchable(),
                ToggleColumn::make('is_in_queue')
                    ->label(__('In Queue'))
                    ->sortable()
                    ->disabled(fn ($record) => in_array($record->phone_number, GetBlackListedPhoneNumbers::execute()))
                    ->onIcon('heroicon-o-check-circle')
                    ->offIcon('heroicon-o-x-circle')
                    ->onColor('success')
                    ->offColor('danger'),
                Tables\Columns\TextColumn::make('status')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\IconColumn::make('is_message_sent')
                    ->label(__('Message Sent'))
                    ->boolean(),
                /**
                Tables\Columns\IconColumn::make('message_status')
                    ->label('Review Status')
                    ->icons(
                        [
                            'heroicon-o-check-circle' => fn($record) => $record->message_status == OrderMessageStatus::PARTIAL_SUCCESS->value,
                            'heroicon-o-exclamation-circle' => fn($record) => $record->message_status == OrderMessageStatus::FULL_SUCCESS->value,
                            'heroicon-o-x-circle' => fn($record) => $record->message_status == OrderMessageStatus::FAILED->value,
                            'heroicon-o-paper-airplane' => fn($record) => $record->message_status == OrderMessageStatus::SENT->value,
                        ]
                    )
                 * */
            ])
            ->defaultSort('order_created_at', 'asc')
            ->filters([
                SelectFilter::make('is_message_sent')
                    ->label(__('Message status'))
                    ->options([
                        '1' => __('Sent'),
                        '0' => __('Not Sent'),
                    ]),
                Tables\Filters\Filter::make('hide_expired')
                    ->label(__('Hide Expired'))
                    ->query(function (Builder $query, $data) {
                        // Only apply the filter if a value is selected (i.e., not null or empty)
                        if (!is_null($data['isActive'])) {
                            $expireDate = Auth::user()->getSetting(Settings::MESSAGE_SEND_AFTER_DAYS->value) ?? 5;
                            //now 00:00
                            $expireDate = now()->subDays($expireDate)->startOfDay();
                            return $query->where('order_created_at', '>=', $expireDate)
                                ->whereNotIn('phone_number', GetBlackListedPhoneNumbers::execute());
                        }

                        return $query;
                    })->default(true)
            ])->persistFiltersInSession()
            ->actions([
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('export')
                        ->label(__('Send Messages'))
                        ->action(function ($records) {
                            if(!AppSettings::permissionCheck(Message::SEND->value)) {
                                Notification::make()
                                    ->title(__('messages.notifications.orders.no_permission'))
                                    ->danger()
                                    ->send();
                                return;
                            }

                            foreach ($records as $record) {
                                SendMessageJob::dispatch($record, Auth::id());
                            }
                            Notification::make()
                                ->title(__('messages.notifications.orders.messages_will_be_sent'))
                                ->success()
                                ->send();
                        })
                        ->icon('heroicon-o-paper-airplane'),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getGloballySearchableAttributes(): array
    {
        return ['order_number', 'customer_name', 'customer_email', 'phone_number', 'status'];
    }

    public static function getNavigationLabel(): string
    {
        return __('Orders');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOrders::route('/'),
            'view' => Pages\ViewOrder::route('/{record}'),
        ];
    }

    public static function getLabel(): ?string
    {
        return __('Orders');
    }

    public static function getNavigationBadge(): ?string
    {
        $delayDay = Auth::user()->getSetting(Settings::MESSAGE_SEND_AFTER_DAYS->value) ?? 5;
        $delayDate = now()->subDays($delayDay)->startOfDay();
        return static::getModel()::where('is_message_sent', false)
            ->where('order_created_at', '>=', $delayDate)
            ->count();
    }
}
