<?php
namespace App\Filament\Resources\TicketResource\Pages;

use App\Filament\Resources\TicketResource;
use App\Models\Ticket;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\Concerns\InteractsWithRecord;
use Filament\Resources\Pages\Page;

class ViewTicket extends Page
{
    use InteractsWithRecord;

    protected static string $resource = TicketResource::class;
    protected static string $view     = 'filament.pages.ticket-view';

    public $message;

    public function mount(int | string $record): void
    {
        $this->record = $this->resolveRecord($record);
        $this->record->load(['user:id,name,is_super_admin','assignedUser:id,name','messages:id,ticket_id,user_id,message,created_at'])->loadCount('messages');
    }

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

    public function reply($id)
    {
        $this->validate([
            'message' => 'required|string|min:2',
        ]);

        $ticket = Ticket::findOrFail($id);

        $ticket->messages()->create([
            'ticket_id' => $ticket->id,
            'user_id'   => auth()->id(),
            'message'   => $this->message,
        ]);

       $this->reset('message');

        Notification::make()
            ->title(__('messages.notifications.tickets.reply_sent'))
            ->body(__('messages.notifications.tickets.reply_sent_body'))
            ->icon('heroicon-o-check-circle')
            ->success()
            ->send();
    }

    public function assignToMe()
    {
        $ticket = $this->getRecord();
        $ticket->assigned_to = auth()->id();
        $ticket->save();

        Notification::make()
            ->title(__('messages.notifications.tickets.assigned_to_you'))
            ->body(__('messages.notifications.tickets.assigned_to_you_body'))
            ->icon('heroicon-o-check-circle')
            ->success()
            ->send();
    }

}
