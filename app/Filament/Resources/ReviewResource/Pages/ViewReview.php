<?php

namespace App\Filament\Resources\ReviewResource\Pages;

use App\Filament\Resources\ReviewResource;
use App\Jobs\PublishReview;
use App\Services\AppSettings;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\Concerns\InteractsWithRecord;
use Filament\Resources\Pages\Page;

class ViewReview extends Page
{
    use InteractsWithRecord;

    protected static string $resource = ReviewResource::class;
    protected static string $view = 'filament.pages.review-details';

    public function mount(int | string $record): void
    {
       $this->record = $this->resolveRecord($record);

       $this->record->load([
            'order:id,order_number,total_amount,currency,status,customer_name,customer_email',
            'item:id,name,sku,price,quantity,img_url',
        ]);

       $this->authorizeAccess();
    }

    protected function authorizeAccess(): void
    {
        abort_unless(static::getResource()::canView($this->getRecord()), 403);
    }

    protected function getHeaderActions(): array
    {
        return [
        Action::make('export')
            ->label(__('Publish'))
            ->disabled($this->record->is_published)
            ->action(function ($record) {
                if(!AppSettings::permissionCheck(\App\Enums\Permissions\Review::PUBLISH->value)) {
                    Notification::make()
                        ->title(__('messages.notifications.reviews.no_permission'))
                        ->danger()
                        ->send();
                    return;
                }

                PublishReview::dispatch($record);
                Notification::make()
                    ->title(__('messages.notifications.reviews.will_be_published'))
                    ->success()
                    ->send();
            })
            ->icon('heroicon-o-paper-airplane'),
    ];
    }
}
