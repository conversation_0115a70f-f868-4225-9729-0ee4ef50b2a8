<?php

namespace App\Filament\Resources\OrderResource\Pages;

use App\Enums\Permissions\Message;
use App\Filament\Resources\OrderResource;
use App\Jobs\SendMessageJob;
use App\Services\AppSettings;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\Concerns\InteractsWithRecord;
use Filament\Resources\Pages\Page;
use Illuminate\Support\Facades\Auth;

class ViewOrder extends Page
{
    use InteractsWithRecord;

    protected static string $resource = OrderResource::class;
    protected static string $view = 'filament.pages.order-info';

    public function mount(int | string $record): void
    {
       $this->record = $this->resolveRecord($record);

       $this->record->load([
            'items:id,order_id,name,quantity,sku,price,img_url,has_review,message_sent',
            'items.review:id,item_id,review_status',
            'messages'
        ]);
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('export')
                ->label(__('Send Message'))
                ->action(function ($record) {
                    if(!AppSettings::permissionCheck(Message::SEND->value)) {
                        Notification::make()
                            ->title(__('messages.notifications.orders.no_permission'))
                            ->danger()
                            ->send();
                        return;
                    }

                    SendMessageJob::dispatch($record, Auth::id());
                    Notification::make()
                        ->title(__('messages.notifications.orders.messages_will_be_sent'))
                        ->success()
                        ->send();
                })
                ->disabled($this->getRecord()->is_message_sent)
                ->icon('heroicon-o-paper-airplane'),
        ];
    }
}
