<?php

namespace App\Filament\Resources\OrderResource\Pages;

use App\Filament\Resources\OrderResource;
use App\Jobs\SyncShopifyOrdersJob;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;

class ListOrders extends ListRecords
{
    protected static string $resource = OrderResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('syncOrders')
                ->label(__('Sync Orders'))
                ->requiresConfirmation()
                ->action('syncOrders')
                ->color('success')
                ->icon('heroicon-o-arrow-path')
                ->modalHeading(__('Sync Orders'))
                ->modalDescription(__('Are you sure you want to sync orders?'))
                ->modalSubmitActionLabel(__('Sync'))
                ->modalWidth('lg')
                ->modalAlignment('center')
        ];
    }

    public function syncOrders()
    {
        $shop = auth()->user();
        SyncShopifyOrdersJob::dispatch($shop);
        Notification::make()
            ->title(__('messages.notifications.orders.sync_started'))
            ->success()
            ->send();
    }
}
