<?php

namespace App\Filament\Resources\MessageTemplatesResource\Pages;

use App\Filament\Resources\MessageTemplatesResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListMessageTemplates extends ListRecords
{
    protected static string $resource = MessageTemplatesResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
