<?php

namespace App\Observers;

use App\Enums\TicketStatuses;
use App\Models\TicketMessage;
use Filament\Notifications\Notification;

class TicketMessageObserver
{
    /**
     * Handle the TicketMessage "created" event.
     */
    public function created(TicketMessage $ticketMessage): void
    {
        $user = $ticketMessage->user;
        $ticket = $ticketMessage->ticket;
        $assignedUser = $ticket->assignedUser;
        $ticketUser = $ticket->user;

        if($user == $assignedUser) {
            $ticket->status = TicketStatuses::Answered->value;
            $ticket->save();
            Notification::make()
                ->title(__('messages.notifications.tickets.answered'))
                ->body(__('messages.notifications.tickets.answered_body', ['id' => $ticket->id]))
                ->success()
                ->sendToDatabase($ticketUser);
        } else if($assignedUser) {
            $ticket->status = TicketStatuses::Open->value;
            $ticket->save();
            Notification::make()
                ->title(__('messages.notifications.tickets.new_message'))
                ->body(__('messages.notifications.tickets.new_message_body', ['id' => $ticket->id]))
                ->success()
                ->sendToDatabase($assignedUser);
        }
    }

    /**
     * Handle the TicketMessage "updated" event.
     */
    public function updated(TicketMessage $ticketMessage): void
    {
        //
    }

    /**
     * Handle the TicketMessage "deleted" event.
     */
    public function deleted(TicketMessage $ticketMessage): void
    {
        //
    }

    /**
     * Handle the TicketMessage "restored" event.
     */
    public function restored(TicketMessage $ticketMessage): void
    {
        //
    }

    /**
     * Handle the TicketMessage "force deleted" event.
     */
    public function forceDeleted(TicketMessage $ticketMessage): void
    {
        //
    }
}
