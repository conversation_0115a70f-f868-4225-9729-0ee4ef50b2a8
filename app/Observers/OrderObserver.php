<?php

namespace App\Observers;

use App\Jobs\EstimateMonhtlyBudgetJob;
use App\Jobs\EstimateMonhtlyMessagesJob;
use App\Jobs\StatsJob;
use App\Models\Order;

class OrderObserver
{
    /**
     * Handle the Order "created" event.
     */
    public function created(Order $order): void
    {
        StatsJob::dispatch($order);
        EstimateMonhtlyBudgetJob::dispatch($order->user);
        EstimateMonhtlyMessagesJob::dispatch($order->user);
    }

    /**
     * Handle the Order "updated" event.
     */
    public function updated(Order $order): void
    {
        //
    }

    /**
     * Handle the Order "deleted" event.
     */
    public function deleted(Order $order): void
    {
        //
    }

    /**
     * Handle the Order "restored" event.
     */
    public function restored(Order $order): void
    {
        //
    }

    /**
     * Handle the Order "force deleted" event.
     */
    public function forceDeleted(Order $order): void
    {
        //
    }
}
