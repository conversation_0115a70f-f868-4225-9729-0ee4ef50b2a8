<?php

namespace App\Observers;

use App\Enums\SenderTypes;
use App\Jobs\StatsJob;
use App\Models\Message;
use App\Services\WhatsAppPricingService;

class MessageObserver
{
    /**
     * Handle the Order "created" event.
     */
    public function created(Message $message): void
    {
        StatsJob::dispatch($message);
    }

    public function saving($message)
    {
        if($message->isDirty('phone_number') && $message->sender == SenderTypes::SYSTEM) {
            $message->price = (new WhatsAppPricingService())->getFinalPrice($message->phone_number);
        }
    }
}
