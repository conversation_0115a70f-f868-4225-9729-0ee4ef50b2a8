<?php

namespace App\Observers;

use App\Models\Ticket;
use App\Models\User;
use Filament\Notifications\Actions\Action;
use Filament\Notifications\Notification;

class TicketObserver
{
    /**
     * Handle the Ticket "created" event.
     */
    public function created(Ticket $ticket): void
    {
        // Notify the super admin when a new ticket is created
        Notification::make()
            ->title(__('messages.notifications.tickets.created'))
            ->body(__('messages.notifications.tickets.created_body'))
            ->icon('heroicon-o-check-circle')
            ->success()
            ->actions([
                Action::make('view')
                    ->button()
                    ->url(route('filament.admin.resources.tickets.view', ['record' => $ticket->id]), shouldOpenInNewTab: false),
            ])
            ->sendToDatabase(User::firstWhere('is_super_admin', true));

    }

    /**
     * Handle the Ticket "updated" event.
     */
    public function updated(Ticket $ticket): void
    {
        // Notify the user who created the ticket when it is updated
        Notification::make()
            ->title(__('messages.notifications.tickets.updated'))
            ->body(__('messages.notifications.tickets.updated_body'))
            ->icon('heroicon-o-check-circle')
            ->success()
            ->actions([
                Action::make('view')
                    ->button()
                    ->url(route('filament.admin.resources.tickets.view', ['record' => $ticket->id]), shouldOpenInNewTab: false),
            ])
            ->sendToDatabase(User::firstWhere('id', $ticket->user_id));

    }

    /**
     * Handle the Ticket "deleted" event.
     */
    public function deleted(Ticket $ticket): void
    {
        //
    }

    /**
     * Handle the Ticket "restored" event.
     */
    public function restored(Ticket $ticket): void
    {
        //
    }

    /**
     * Handle the Ticket "force deleted" event.
     */
    public function forceDeleted(Ticket $ticket): void
    {
        //
    }
}
