<?php

namespace App\Observers;

use App\Events\ReviewIsDoneEvent;
use App\Jobs\StatsJob;
use App\Models\Review;

class ReviewObserver
{
    /**
     * Handle the Review "created" event.
     */
    public function created(Review $review): void
    {
        StatsJob::dispatch($review);
    }

    /**
     * @param Review $review
     * @return void
     */
    public function saving(Review $review): void
    {

    }

    /**
     * Handle the Review "updated" event.
     */
    public function updated(Review $review): void
    {
        if($review->isDirty('done') && $review->done)
        {
            event(new ReviewIsDoneEvent($review));
        }
    }

    /**
     * Handle the Review "deleted" event.
     */
    public function deleted(Review $review): void
    {
        //
    }

    /**
     * Handle the Review "restored" event.
     */
    public function restored(Review $review): void
    {
        //
    }

    /**
     * Handle the Review "force deleted" event.
     */
    public function forceDeleted(Review $review): void
    {
        //
    }
}
