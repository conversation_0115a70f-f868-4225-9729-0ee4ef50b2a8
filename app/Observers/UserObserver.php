<?php

namespace App\Observers;

use App\Enums\StoreTypes;
use App\Models\User;
use App\Services\Shops\Shopify\CheckIfShopifyRequest;
use App\Services\User\Defaults\DefaultSettingsGenerator;
use App\Services\User\Defaults\DefaultTemplatesGenerator;

class UserObserver
{
    /**
     * Handle the User "created" event.
     */
    public function created(User $user): void
    {
        (new DefaultSettingsGenerator())->execute($user);
        (new DefaultTemplatesGenerator())->execute($user);

        if(CheckIfShopifyRequest::execute()) {
            $user->store_type = StoreTypes::SHOPIFY->value;
            $user->save();
        }
    }

    /**
     * Handle the User "updated" event.
     */
    public function updated(User $user): void
    {
        //
    }

    /**
     * Handle the User "deleted" event.
     */
    public function deleted(User $user): void
    {
        //
    }

    /**
     * Handle the User "restored" event.
     */
    public function restored(User $user): void
    {
        //
    }

    /**
     * Handle the User "force deleted" event.
     */
    public function forceDeleted(User $user): void
    {
        //
    }
}
