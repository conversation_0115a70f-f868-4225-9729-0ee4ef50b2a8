<?php namespace App\Jobs;

use App\Enums\StoreTypes;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Osiset\ShopifyApp\Objects\Values\ShopDomain;
use stdClass;

class ShopifyAppUninstalledJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Shop's myshopify domain
     *
     * @var ShopDomain|string
     */
    public $shopDomain;

    /**
     * The webhook data
     *
     * @var object
     */
    public $data;

    /**
     * Create a new job instance.
     *
     * @param string   $shopDomain The shop's myshopify domain.
     * @param stdClass $data       The webhook data (JSON decoded).
     *
     * @return void
     */
    public function __construct($shopDomain, $data)
    {
        $this->shopDomain = $shopDomain;
        $this->data = $data;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        // Convert domain
        $this->shopDomain = ShopDomain::fromNative($this->shopDomain);

        // Do what you wish with the data
        // Access domain name as $this->shopDomain->toNative()
        $user = \App\Models\User::where('name', $this->shopDomain->toNative())
            ->where('store_type', StoreTypes::SHOPIFY->value)
            ->first();

        if(!$user) {
            return; // User not found, nothing to do
        }

        $user->settings()->delete();
        $user->plan_id = null; // Remove plan ID
        $user->save(); // Save changes to the user
        $user->delete(); // Soft delete the user
        //Remove model settings// Delete user settings

        Log::info('Shopify App Uninstalled', [
            'shop_domain' => $this->shopDomain->toNative(),
            'user_id' => $user->id,
        ]);
    }
}
