<?php namespace App\Jobs;

use App\Enums\StoreTypes;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Osiset\ShopifyApp\Objects\Values\ShopDomain;
use stdClass;

class ShopifyOrderUpdateJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Shop's myshopify domain
     *
     * @var ShopDomain|string
     */
    public $shopDomain;

    /**
     * The webhook data
     *
     * @var object
     */
    public $data;

    /**
     * Create a new job instance.
     *
     * @param string   $shopDomain The shop's myshopify domain.
     * @param stdClass $data       The webhook data (JSON decoded).
     *
     * @return void
     */
    public function __construct($shopDomain, $data)
    {
        $this->shopDomain = $shopDomain;
        $this->data = $data;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        // Convert domain
        $this->shopDomain = ShopDomain::fromNative($this->shopDomain);
        $user = User::where('store_type', StoreTypes::SHOPIFY->value)
            ->where('name', $this->shopDomain->toNative())
            ->first();
        if (!$user) {
            Log::error('ShopifyOrderUpdate: User not found for shop domain: ' .  $this->shopDomain->toNative());
            return;
        }

        // Log the order update
        Log::info('ShopifyOrderUpdate: Order update received for shop domain: ' .  $this->shopDomain->toNative());
        // Dispatch the job to sync orders
        SyncShopifyOrdersJob::dispatch($user);
    }
}
