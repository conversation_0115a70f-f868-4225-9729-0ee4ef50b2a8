<?php

namespace App\Jobs;

use App\Models\Integrations\IntegrationsEngine;
use App\Models\Review;
use Filament\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class PublishReview implements ShouldQueue, ShouldBeUnique
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(protected Review $review)
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        if (!$this->review->user->hasPermissionTo(\App\Enums\Permissions\Review::PUBLISH->value)) {
            Notification::make()
                ->title(__('messages.notifications.reviews.no_permission_publish'))
                ->danger()
                ->sendToDatabase($this->review->user);

            return;
        }

        $user = $this->review->user;
        $userActiveIntegrations = (new IntegrationsEngine())->getAllActivesCodesByUser($user);
        if (empty($userActiveIntegrations)) {
            Notification::make()
                ->title(__('messages.notifications.reviews.no_active_integrations'))
                ->danger()
                ->sendToDatabase($user);
            return;
        }

        $publishedCodes = $this->review->integration_codes ?? [];
        foreach ($userActiveIntegrations as $integrationCode) {
            if (in_array($integrationCode, $publishedCodes)) {
                continue;
            }

            PublishReviewActive::dispatch($this->review, $integrationCode);
        }
    }

    public function uniqueId(): int
    {
        return $this->review->id;
    }
}
