<?php

namespace App\Jobs\ShopifyCompliance;

use App\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class HandleShopRedactJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public readonly array $payload
    ){}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $shopDomain = $this->payload['shop_domain'] ?? null;

        $shop = User::select('id', 'name')->firstWhere('name', $shopDomain);

        if (! $shop) {
            Log::error('Shop not found', ['shop_domain' => $shopDomain]);
            return;
        }

        try {
            Log::info('Redacting shop data started');
            
            DB::transaction(function () use ($shop) {
                DB::table('conversations')->where('user_id', $shop->id)->delete();
                DB::table('messages')->where('user_id', $shop->id)->delete();
                DB::table('reviews')->where('user_id', $shop->id)->delete();
                DB::table('items')->where('user_id', $shop->id)->delete();
                DB::table('orders')->where('user_id', $shop->id)->delete();
                DB::table('message_templates')->where('user_id', $shop->id)->delete();
                DB::table('charges')->where('user_id', $shop->id)->delete();
                DB::table('stats')->where('user_id', $shop->id)->delete();
                DB::table('users')->where('id', $shop->id)->delete();
            });

            Log::info('Shop data redacted successfully', [
                'shop_domain' => $shopDomain,
            ]);
        } catch (\Exception $e) {
            Log::error('Error during shop data redaction', [
                'shop_domain' => $shopDomain,
                'error' => $e->getMessage(),
            ]);
        }
    }
}
