<?php

namespace App\Jobs\ShopifyCompliance;

use App\Models\Order;
use App\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;

class HandleCustomerDataRequestJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public readonly array $payload
    ){}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $shopDomain = $this->payload['shop_domain'];
        $customerEmail = $this->payload['customer']['email'] ?? null;
        $customerPhone = $this->payload['customer']['phone'] ?? null;
        $dataRequestId = $this->payload['data_request']['id'] ?? null;

        $shop = User::select('id', 'name')->firstWhere('name', $shopDomain);

        if (! $shop) {
            Log::error('Shop not found', ['shop_domain' => $shopDomain]);
            return;
        }

        $orders = Order::where('user_id', $shop->id)
            ->where(function ($query) use ($customerEmail, $customerPhone) {
                $query->when($customerEmail, fn($q) => $q->where('customer_email', $customerEmail))
                    ->when($customerPhone, fn($q) => $q->orWhere('phone_number', $customerPhone));
            })->get();

        if ($orders->isEmpty()) {
            Log::info('No orders found for customer data request', [
                'shop_domain' => $shopDomain,
                'customer_email' => $customerEmail,
                'customer_phone' => $customerPhone,
            ]);
            return;
        }

        // Generate CSV
        $filename = 'customer-data-'. $dataRequestId . now()->timestamp . '.csv';
        $path = storage_path("app/$filename");
        $csv = fopen($path, 'w');

        fputcsv($csv, ['Order ID', 'Customer Name', 'Email', 'Phone', 'Created At']);

        foreach ($orders as $order) {
            fputcsv($csv, [
                $order->order_number,
                $order->customer_name,
                $order->customer_email,
                $order->phone_number,
                $order->created_at
            ]);
        }

        fclose($csv);

        // Send to merchant
        if ($shop && $shop->merchant_email) {
            // Mail::to($merchant->email)->send(); // need to send merchent email with attachment
        }
        unlink($path);
    }
}
