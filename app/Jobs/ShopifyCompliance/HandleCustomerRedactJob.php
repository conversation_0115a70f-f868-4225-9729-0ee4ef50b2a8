<?php

namespace App\Jobs\ShopifyCompliance;

use App\Models\Order;
use App\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;

class HandleCustomerRedactJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public readonly array $payload
    ){}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $shopDomain = $this->payload['shop_domain'] ?? null;
        $customerEmail = $this->payload['customer']['email'] ?? null;
        $customerPhone = $this->payload['customer']['phone'] ?? null;

        $shop = User::select('id', 'name')->firstWhere('name', $shopDomain);

        if (!$shop) {
            Log::error('Shop not found', ['shop_domain' => $shopDomain]);
            return;
        }

        try {
           Order::where('user_id', $shop->id)
            ->where(function ($query) use ($customerEmail, $customerPhone) {
                $query->when($customerEmail, fn($q) => $q->where('customer_email', $customerEmail))
                    ->when($customerPhone, fn($q) => $q->orWhere('phone_number', $customerPhone));
            })
            ->update([
                'customer_name'  => null,
                'customer_email' => null,
                'phone_number'   => null,
            ]);

           Log::info('Customer data redacted successfully', [
                'shop_domain' => $shopDomain,
                'customer_email' => $customerEmail,
                'customer_phone' => $customerPhone,
            ]);

        } catch (\Exception $e) {
            Log::error('Error during customer data redaction', [
                'shop_domain' => $shopDomain,
                'customer_email' => $customerEmail,
                'customer_phone' => $customerPhone,
                'error' => $e->getMessage(),
            ]);
        }
    }
}
