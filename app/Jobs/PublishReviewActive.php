<?php

namespace App\Jobs;

use App\Models\Integrations\IntegrationsEngine;
use App\Models\Review;
use Filament\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;

class PublishReviewActive implements ShouldQueue, ShouldBeUnique
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(protected Review $review, protected string $integrationCode)
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $integration = (new IntegrationsEngine())->getByCode($this->integrationCode);
        if (!$integration) {
            return;
        }

        $integration->setUser($this->review->user);

        try {
            if($integration->publish($this->review)) {
                Notification::make()
                    ->title(__('messages.notifications.reviews.published_successfully', ['integration' => $integration->getName()]))
                    ->success()
                    ->sendToDatabase($this->review->user);
            } else {
                throw new \Exception(__('Failed to publish review via integration: :integration', ['integration' => $integration->getName()]));
            }
        } catch (\Exception $e) {
            Notification::make()
                ->title(__('messages.notifications.reviews.publish_failed', ['integration' => $integration->getName()]))
                ->danger()
                ->sendToDatabase($this->review->user);
            Log::error("Failed to publish review via integration {$this->integrationCode}: " . $e->getMessage() . ' - Trace: ' . $e->getTraceAsString());
        }
    }

    public function uniqueId(): string
    {
        return $this->review->id . '_' . $this->integrationCode;
    }
}
