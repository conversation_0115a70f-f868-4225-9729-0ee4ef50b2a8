<?php

namespace App\Jobs;

use App\Enums\Permissions\Message;
use App\Models\Order;
use App\Services\Messengers\ConversationGenerator;
use App\Services\Messengers\MessengerEngine;
use Filament\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;

class SendMessageJob implements ShouldQueue, ShouldBeUnique
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(protected Order $order, protected ?int $userId = null)
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            if (!$this->order->user->hasPermissionTo(Message::SEND->value)) {
                Notification::make()
                    ->title(__('messages.notifications.messages.no_permission_send'))
                    ->danger()
                    ->sendToDatabase($this->order->user);
                return;
            }

            if (!$this->order->phone_number) {
                Notification::make()
                    ->title(__('messages.notifications.messages.no_phone_number', ['s' => $this->order->cleanOrderNumber()]))
                    ->danger()
                    ->sendToDatabase($this->order->user);

                return;
            }

            $messenger = MessengerEngine::getActiveMessenger();
            (new ConversationGenerator($messenger))->execute($this->order);
        } catch (\Throwable $e) {
            Log::error('Error sending message: ' . $e->getMessage() . ' - Order ID: ' . $this->order->id . ' trace: ' . $e->getTraceAsString());
            if($this->userId) {
                Notification::make()
                    ->title(__('messages.notifications.messages.could_not_be_sent', ['s' => $this->order->cleanOrderNumber()]))
                    ->body(__('messages.notifications.messages.error', ['s' => $e->getMessage()]))
                    ->danger()
                    ->sendToDatabase($this->order->user);
            }
        }
    }

    /**
     * @return int
     */
    public function uniqueId(): int
    {
        return $this->order->id;
    }
}
