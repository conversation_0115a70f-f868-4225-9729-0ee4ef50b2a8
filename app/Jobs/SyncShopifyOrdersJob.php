<?php

namespace App\Jobs;

use App\Models\User;
use App\Services\Shops\Shopify\ShopifyOrderSync;
use Filament\Notifications\Notification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

final class SyncShopifyOrdersJob implements ShouldQueue
{
   use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        public readonly User $shop
    ) {}

    public function handle(ShopifyOrderSync $service): void
    {
        if ($service->syncOrders($this->shop)) {
            SyncShopifyOrdersJob::dispatch($this->shop);
        } else {
            Notification::make()
                ->title(__('messages.notifications.orders.sync_completed'))
                ->success()
                ->sendToDatabase($this->shop);
        }
    }
}
