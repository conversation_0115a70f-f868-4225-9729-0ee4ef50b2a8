<?php

namespace App\Jobs;

use App\Models\User;
use App\Services\Message\EstimateMonthlyMessages;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class EstimateMonhtlyMessagesJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(public User $user)
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        (new EstimateMonthlyMessages())->execute($this->user);
    }
}
