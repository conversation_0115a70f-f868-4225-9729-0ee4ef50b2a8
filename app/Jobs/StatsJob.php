<?php

namespace App\Jobs;

use App\Services\Stats\StatsService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Queue\Queueable;

class StatsJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(public Model $model)
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        (new StatsService())->execute($this->model);
    }
}
