<?php

namespace App\Enums;

enum ConversationMessageTypes: string
{
    case STARTER_MESSAGE       = 'starter';         // İlk mesaj, ürün listesi
    case STARTER_REST_MESSAGE  = 'starter_rest';    // İlk ürün yorumu sonrası kalan ürünler
    case PRODUCT_SELECT        = 'product_select';  // Kullanıcının ürün numarası seçimi
    case REVIEW_ASK_MESSAGE    = 'review_ask';      // Yorum istenen mesaj
    case REVIEW_RESPONSE       = 'review_response'; // Kullanıcının yorum cevabı
    case REVIEW_APPROVE_MESSAGE= 'review_approve';  // Yorum onayı
    case RATE_ASK_MESSAGE      = 'rate_ask';        // Puan sorusu
    case RATE_RESPONSE         = 'rate_response';   // Kullanıcının puanı
    case RATE_APPROVE_MESSAGE  = 'rate_approve';    // Puan onayı
    case REVIEW_THANK_MESSAGE  = 'review_thank';    // Ürün için teşekkür
    case THANK_YOU_MESSAGE     = 'thank_you';       // Tüm ürünler tamamlanınca teşekkür
    case RATE_OFFER_MESSAGE = 'rate_offer';
    case COMMENT_OFFER_MESSAGE = 'comment_offer';
    case COMMENT_RESPONSE = 'comment_response';

}
