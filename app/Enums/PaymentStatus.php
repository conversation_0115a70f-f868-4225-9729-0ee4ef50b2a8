<?php

namespace App\Enums;

enum PaymentStatus: string
{
    case Authorized           = 'AUTHORIZED';
    case Expired              = 'EXPIRED';
    case Paid                 = 'PAID';
    case PartiallyPaid        = 'PARTIALLY_PAID';
    case PartiallyRefunded    = 'PARTIALLY_REFUNDED';
    case Pending              = 'PENDING';
    case Refunded             = 'REFUNDED';
    case Voided               = 'VOIDED';

    public function color(): string
    {
        return match ($this) {
            self::Authorized         => 'bg-amber-50 text-amber-700 border border-amber-200',
            self::Expired            => 'bg-red-50 text-red-700 border border-red-200',
            self::Paid               => 'bg-green-50 text-green-700 border border-green-200',
            self::PartiallyPaid      => 'bg-yellow-50 text-yellow-700 border border-yellow-200',
            self::PartiallyRefunded  => 'bg-yellow-50 text-yellow-700 border border-yellow-200',
            self::Pending            => 'bg-blue-50 text-blue-700 border border-blue-200',
            self::Refunded           => 'bg-purple-50 text-purple-700 border border-purple-200',
            self::Voided             => 'bg-gray-100 text-gray-700 border border-gray-300',
        };
    }

    public function label(): string
    {
        return ucwords(str_replace('_', ' ', strtolower($this->value)));
    }

    public static function tryFromValue(string $value): ?self
    {
        return self::tryFrom(strtoupper($value));
    }
}
