<?php

namespace App\Enums;

enum TicketStatuses: string
{
    case Open = 'open';
    case InProgress = 'in_progress';
    case Answered = 'answered';
    case Closed = 'closed';
    case OnHold = 'on_hold';
    case Resolved = 'resolved';
    case Reopened = 'reopened';
    case Canceled = 'canceled';

    public function label(): string
    {
        return match ($this) {
            self::Open => 'Open',
            self::InProgress => 'In Progress',
            self::Closed => 'Closed',
            self::OnHold => 'On Hold',
            self::Resolved => 'Resolved',
            self::Reopened => 'Reopened',
            self::Canceled => 'Canceled',
            self::Answered => 'Answered',
        };
    }

    public static function labelFromValue(string $value): ?string
    {
        return self::tryFrom($value)?->label();
    }
}