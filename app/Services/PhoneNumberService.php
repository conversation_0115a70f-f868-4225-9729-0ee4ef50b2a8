<?php

namespace App\Services;

use libphonenumber\NumberParseException;
use libphonenumber\PhoneNumberFormat;
use libphonenumber\PhoneNumberUtil;

class PhoneNumberService
{
    private PhoneNumberUtil $phoneUtil;
    private static array $countryMap = [
        'turkey' => 'TR', 'türkiye' => 'TR',
        'united states' => 'US', 'usa' => 'US', 'america' => 'US',
        'united kingdom' => 'GB', 'uk' => 'GB', 'great britain' => 'GB', 'england' => 'GB',
        'germany' => 'DE', 'deutschland' => 'DE',
        'france' => 'FR', 'spain' => 'ES', 'españa' => 'ES',
        'italy' => 'IT', 'italia' => 'IT',
        'canada' => 'CA', 'australia' => 'AU', 'japan' => 'JP',
        'china' => 'CN', 'india' => 'IN',
        'brazil' => 'BR', 'brasil' => 'BR',
        'russia' => 'RU', 'russian federation' => 'RU',
        'south korea' => 'KR', 'korea' => 'KR',
        'mexico' => 'MX', 'méxico' => 'MX',
        'netherlands' => 'NL', 'holland' => 'NL',
        'belgium' => 'BE', 'sweden' => 'SE', 'norway' => 'NO',
        'denmark' => 'DK', 'finland' => 'FI', 'poland' => 'PL',
        'czech republic' => 'CZ', 'austria' => 'AT',
        'switzerland' => 'CH', 'portugal' => 'PT',
        'greece' => 'GR', 'ireland' => 'IE',
        'south africa' => 'ZA', 'egypt' => 'EG',
        'israel' => 'IL', 'saudi arabia' => 'SA',
        'united arab emirates' => 'AE', 'uae' => 'AE',
        'singapore' => 'SG', 'thailand' => 'TH',
        'malaysia' => 'MY', 'indonesia' => 'ID',
        'philippines' => 'PH', 'vietnam' => 'VN',
        'new zealand' => 'NZ', 'argentina' => 'AR',
        'chile' => 'CL', 'colombia' => 'CO',
        'peru' => 'PE', 'venezuela' => 'VE',
        'ukraine' => 'UA', 'romania' => 'RO',
        'hungary' => 'HU', 'bulgaria' => 'BG',
        'croatia' => 'HR', 'serbia' => 'RS',
        'slovenia' => 'SI', 'slovakia' => 'SK',
        'lithuania' => 'LT', 'latvia' => 'LV', 'estonia' => 'EE'
    ];

    public function __construct()
    {
        $this->phoneUtil = PhoneNumberUtil::getInstance();
    }

    /**
     * Format phone number using ISO country code
     *
     * @param string $phoneNumber
     * @param string $countryIso - ISO code (US, TR, GB, etc.)
     * @return string|null - Returns E164 formatted number or null if invalid
     */
    public function formatWithIso(string $phoneNumber, string $countryIso): ?string
    {
        $phoneNumber = trim($phoneNumber);
        $countryIso = strtoupper(trim($countryIso));

        if (empty($phoneNumber) || empty($countryIso)) {
            return null;
        }

        // Validate ISO code
        if ($this->phoneUtil->getCountryCodeForRegion($countryIso) === 0) {
            return null;
        }

        return $this->processPhoneNumber($phoneNumber, $countryIso);
    }

    /**
     * Format phone number using country name
     *
     * @param string $phoneNumber
     * @param string $countryName - Country name (United States, Turkey, etc.)
     * @return string|null - Returns E164 formatted number or null if invalid
     */
    public function formatWithCountry(string $phoneNumber, string $countryName): ?string
    {
        $phoneNumber = trim($phoneNumber);
        $countryName = trim($countryName);

        if (empty($phoneNumber) || empty($countryName)) {
            return null;
        }

        // Get ISO code from country name
        $countryIso = self::$countryMap[strtolower($countryName)] ?? null;
        if (!$countryIso) {
            return null;
        }

        return $this->processPhoneNumber($phoneNumber, $countryIso);
    }

    /**
     * Format phone number - works with both ISO codes and country names
     *
     * @param string $phoneNumber
     * @param string $country - ISO code (US, TR) or country name (United States, Turkey)
     * @return string|null - Returns E164 formatted number or null if invalid
     */
    public function formatPhone(string $phoneNumber, string $country): ?string
    {
        $phoneNumber = trim($phoneNumber);
        $country = trim($country);

        if (empty($phoneNumber) || empty($country)) {
            return null;
        }

        // Get ISO code
        $countryIso = $this->getCountryIso($country);
        if (!$countryIso) {
            return null;
        }

        return $this->processPhoneNumber($phoneNumber, $countryIso);
    }

    /**
     * Core phone number processing logic
     */
    private function processPhoneNumber(string $phoneNumber, string $countryIso): ?string
    {
        try {
            // Check if already has country code
            if ($this->hasCountryCode($phoneNumber)) {
                $phoneNumberObj = $this->phoneUtil->parse($phoneNumber, null);
            } else {
                $phoneNumberObj = $this->phoneUtil->parse($phoneNumber, $countryIso);
            }

            // Validate and return formatted number
            return $this->phoneUtil->isValidNumber($phoneNumberObj)
                ? $this->phoneUtil->format($phoneNumberObj, PhoneNumberFormat::E164)
                : null;

        } catch (NumberParseException $e) {
            return null;
        }
    }

    /**
     * Batch format multiple phone numbers using ISO codes
     *
     * @param array $phoneNumbers - [['phone' => '...', 'country_iso' => '...'], ...]
     * @return array - ['original_phone' => 'formatted_phone_or_null', ...]
     */
    public function batchFormatWithIso(array $phoneNumbers): array
    {
        $results = [];

        foreach ($phoneNumbers as $item) {
            $phone = $item['phone'] ?? '';
            $countryIso = $item['country_iso'] ?? '';
            $formatted = $this->formatWithIso($phone, $countryIso);
            $results[$phone] = $formatted;
        }

        return $results;
    }

    /**
     * Batch format multiple phone numbers using country names
     *
     * @param array $phoneNumbers - [['phone' => '...', 'country_name' => '...'], ...]
     * @return array - ['original_phone' => 'formatted_phone_or_null', ...]
     */
    public function batchFormatWithCountry(array $phoneNumbers): array
    {
        $results = [];

        foreach ($phoneNumbers as $item) {
            $phone = $item['phone'] ?? '';
            $countryName = $item['country_name'] ?? '';
            $formatted = $this->formatWithCountry($phone, $countryName);
            $results[$phone] = $formatted;
        }

        return $results;
    }

    /**
     * Batch format multiple phone numbers
     *
     * @param array $phoneNumbers - [['phone' => '...', 'country' => '...'], ...]
     * @return array - ['original_phone' => 'formatted_phone_or_null', ...]
     */
    public function batchFormat(array $phoneNumbers): array
    {
        $results = [];

        foreach ($phoneNumbers as $item) {
            $phone = $item['phone'] ?? '';
            $country = $item['country'] ?? '';
            $formatted = $this->formatPhone($phone, $country);
            $results[$phone] = $formatted;
        }

        return $results;
    }

    /**
     * Get country ISO code from name or validate existing ISO
     */
    private function getCountryIso(string $country): ?string
    {
        // Check if it's already a valid ISO code
        if (strlen($country) <= 3 && ctype_alpha($country)) {
            $countryIso = strtoupper($country);
            if ($this->phoneUtil->getCountryCodeForRegion($countryIso) !== 0) {
                return $countryIso;
            }
        }

        // Try as country name
        return self::$countryMap[strtolower($country)] ?? null;
    }

    /**
     * Check if phone number has country code
     */
    private function hasCountryCode(string $phoneNumber): bool
    {
        $cleaned = preg_replace('/[\s\-\(\)\.]+/', '', $phoneNumber);

        if (preg_match('/^(\+|00)/', $cleaned)) {
            return true;
        }

        try {
            $this->phoneUtil->parse($phoneNumber, null);
            return true;
        } catch (NumberParseException $e) {
            return false;
        }
    }
}