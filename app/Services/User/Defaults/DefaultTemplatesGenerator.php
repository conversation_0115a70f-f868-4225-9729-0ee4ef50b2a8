<?php

namespace App\Services\User\Defaults;

use App\Enums\ConversationMessageTypes as MessageTypes;
use App\Models\MessageTemplates;
use App\Models\User;

class DefaultTemplatesGenerator
{
    public function execute(User $user): void
    {
        $templates = [
            MessageTypes::STARTER_MESSAGE->value => <<<TEXT
Hi {{customer_name}} 👋, thank you for your order **#{{order_number}}**! 🛍️

We’d love to hear your thoughts on what you’ve received. Let's go through your products one by one. 📝
TEXT,

            MessageTypes::RATE_OFFER_MESSAGE->value => <<<TEXT
Would you like to rate **{{name}}**? 🌟

Reply with **yes** or **no**.
TEXT,

            MessageTypes::RATE_ASK_MESSAGE->value => <<<TEXT
On a scale from 1 to 5, how would you rate **{{name}}**? ✨

Reply with a number like **5**, **4**, etc.
TEXT,

            MessageTypes::COMMENT_OFFER_MESSAGE->value => <<<TEXT
Would you like to leave a comment for **{{name}}**? 💬

Reply with **yes** or **no**.
TEXT,

            MessageTypes::REVIEW_ASK_MESSAGE->value => <<<TEXT
Please write your comment about **{{name}}** below 👇

We’d love to hear what you think!
TEXT,

            MessageTypes::REVIEW_THANK_MESSAGE->value => <<<TEXT
Thanks for reviewing **{{name}}**! 🙏

Your feedback is incredibly valuable to us.
TEXT,

            MessageTypes::THANK_YOU_MESSAGE->value => <<<TEXT
Thanks again for shopping with us! 🧡

We hope you enjoyed your order **#{{order_number}}** and we truly appreciate your time and thoughts.
TEXT,
        ];

        foreach ($templates as $type => $template) {
            MessageTemplates::updateOrCreate(
                [
                    'user_id' => $user->id,
                    'type' => $type,
                ],
                [
                    'name' => ucwords(str_replace('_', ' ', $type)),
                    'template' => $template,
                ]
            );
        }
    }
}
