<?php

namespace App\Services\User;

use App\Enums\Settings;
use App\Models\User;

class GetBlackListedPhoneNumbers
{
    public static function execute(?User $user = null)
    {
        if (!$user) {
            $user = auth()->user();
        }

        // Assuming $user->getSetting(Settings::BLACK_LISTED_PHONE_NUMBERS->value) returns an array
        $blackListedPhoneNumbers = $user->getSetting(Settings::BLACK_LISTED_PHONE_NUMBERS->value);

        // Check if the setting is an array and it's not empty
        if (is_array($blackListedPhoneNumbers) && !empty($blackListedPhoneNumbers)) {
            // Filter and sanitize phone numbers
            $phoneNumbers = array_map(function ($item) {
                // Check if 'phone_number' is set and sanitize it (remove non-numeric characters)
                if (isset($item['phone_number']) && is_string($item['phone_number'])) {
                    return $item['phone_number'];  // Remove non-numeric characters
                }
                return null;
            }, $blackListedPhoneNumbers);

            // Remove null values (if any)
            $phoneNumbers = array_filter($phoneNumbers, function ($value) {
                return $value !== null;
            });

            // Ensure uniqueness
            return array_unique($phoneNumbers);

        } else {
            // Handle the case where the setting is not a valid array
            return [];
        }
    }
}