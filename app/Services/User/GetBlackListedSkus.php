<?php

namespace App\Services\User;

use App\Enums\Settings;
use App\Models\User;

class GetBlackListedSkus
{
    public static function execute(?User $user = null)
    {
        if (!$user) {
            $user = auth()->user();
        }

        // Assuming $user->getSetting(Settings::BLACK_LISTED_SKUS->value) returns an array
        $blackListedSkus = $user->getSetting(Settings::BLACK_LISTED_PRODUCT_SKUS->value);

        // Check if the setting is an array and it's not empty
        if (is_array($blackListedSkus) && !empty($blackListedSkus)) {
            // Filter and sanitize phone numbers
            $skus = array_map(function ($item) {
                // Check if 'phone_number' is set and sanitize it (remove non-numeric characters)
                if (isset($item['sku']) && is_string($item['sku'])) {
                    return $item['sku'];  // Remove non-numeric characters
                }
                return null;
            }, $blackListedSkus);

            // Remove null values (if any)
            $skus = array_filter($skus, function ($value) {
                return $value !== null;
            });

            // Ensure uniqueness
            return array_unique($skus);

        } else {
            // Handle the case where the setting is not a valid array
            return [];
        }
    }
}