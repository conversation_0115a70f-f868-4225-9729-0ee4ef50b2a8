<?php

namespace App\Services\User\Balance;

use App\Models\Message;

class BeforeMessageSend
{
    public function execute(Message $message): bool
    {
        //check if user can send message
        $user = $message->user;
        $userMaxItems = $user->plan?->items_count ?? 0;
        $stats = $user->stats()->where('year', date('Y'))->where('month', date('m'))->first();
        $totalItems = $stats ? $stats->total_reviews : 0;
        $remainingItems = $userMaxItems - $totalItems;
        return $remainingItems > 0;
    }
}