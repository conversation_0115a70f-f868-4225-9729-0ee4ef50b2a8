<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use libphonenumber\NumberParseException;
use libphonenumber\PhoneNumberUtil;

class WhatsAppPricingService
{
    /**
     * WhatsApp utility pricing per country (USD)
     * Last updated: 2025-06-01
     * Source: https://developers.facebook.com/docs/whatsapp/pricing/
     */
    protected array $utilityRates = [
        'AR' => 0.0340, 'BR' => 0.0080, 'CL' => 0.0200, 'CO' => 0.0002, 'EG' => 0.0052,
        'FR' => 0.0300, 'DE' => 0.0550, 'IN' => 0.0014, 'ID' => 0.0200, 'IL' => 0.0053,
        'IT' => 0.0300, 'MY' => 0.0140, 'MX' => 0.0100, 'NL' => 0.0500, 'NG' => 0.0067,
        'PK' => 0.0054, 'PE' => 0.0200, 'RU' => 0.0400, 'SA' => 0.0115, 'ZA' => 0.0076,
        'ES' => 0.0200, 'TR' => 0.0053, 'AE' => 0.0157, 'GB' => 0.0220, 'US' => 0.0040,
        'CA' => 0.0040, // North America
        'KE' => 0.0040, // Rest of Africa
        'AU' => 0.0157, // Rest of Asia Pacific
        'UA' => 0.0353, // Rest of Central & Eastern Europe
        'AR-REST' => 0.0113, // Rest of Latin America
        'IR' => 0.0157, // Rest of Middle East
        'BE' => 0.0300, // Rest of Western Europe
        'OTHER' => 0.0077,
    ];

    /**
     * Detect country ISO code from phone number (TR, US, etc.)
     */
    protected function getCountryCode(string $phone): ?string
    {
        try {
            $phoneUtil = PhoneNumberUtil::getInstance();
            $number = $phoneUtil->parse($phone, null);
            return $phoneUtil->getRegionCodeForNumber($number);
        } catch (NumberParseException $e) {
            return null;
        }
    }

    /**
     * Get cached utility price per country (USD), 1-day TTL
     */
    protected function getUtilityPrice(string $countryCode): ?float
    {
        return Cache::remember("whatsapp_utility_rate_$countryCode", now()->addDay(), function () use ($countryCode) {
            return $this->utilityRates[$countryCode] ?? $this->utilityRates['OTHER'];
        });
    }

    /**
     * Public method to get utility cost for a phone number
     */
    public function getUtilityCost(string $phone): ?float
    {
        $countryCode = $this->getCountryCode($phone);

        if (!$countryCode) {
            return null;
        }

        return $this->getUtilityPrice($countryCode);
    }

    public function getFinalPrice($phoneNumber)
    {
        $WpPrice = $this->getUtilityCost($phoneNumber);
        return $WpPrice + 0.005;
    }
}
