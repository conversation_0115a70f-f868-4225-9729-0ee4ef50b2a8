<?php

namespace App\Services;

use App\Models\User;

class AppSettings
{
    private static bool $customNotificationStatus = false;

    public static function setCustomNotificationStatus(bool $status): void
    {
        self::$customNotificationStatus = $status;
    }

    public static function getCustomNotificationStatus(): bool
    {
        return self::$customNotificationStatus;
    }

    public static function permissionCheck($permission, $user = null)
    {
        if($user) {
            return $user->hasPermissionTo($permission);
        }

        return auth()->user()->hasPermissionTo($permission);
    }

    public static  function getMessageCost(User $user = null): float
    {
        return 0.05;
    }
}