<?php

namespace App\Services\Stats;

use App\Models\Review;
use Illuminate\Database\Eloquent\Model;

class ReviewStatsService extends AbstractStats
{
    public function execute(Model|Review $model)
    {
        $userId = $model->user_id;
        $year =  date('Y');
        $month = date('m');
        $reviews = Review::where('user_id', $userId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->avg('rating');

        $doneReviews = Review::where('user_id', $userId)
            ->whereYear('updated_at', $year)
            ->whereMonth('updated_at', $month)
            ->where('done', true)
            ->count();

        $this->stats->update([
            'average_rating' => $reviews ?? 0,
            'done_reviews' => $doneReviews ?? 0,
        ]);

    }
}