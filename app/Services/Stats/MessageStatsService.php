<?php

namespace App\Services\Stats;

use App\Models\Message;
use App\Services\AppSettings;
use App\Services\Stats\AbstractStats;
use Illuminate\Database\Eloquent\Model;

class MessageStatsService extends AbstractStats
{
    public function execute(Message|Model $model)
    {
        $user = $model->user;
        $year =  date('Y');
        $month = date('m');

        $messages = Message::where('user_id', $user->id)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->get();

        $totalMessages = $messages->count() ?? 0;
        $totalCostCurrentMonth = round($messages->sum('price'), 2);

        $userMaxItems = $user->plan?->items_count ?? 0;
        $stats          = $user->stats()->where('year', date('Y'))->where('month', date('m'))->first();
        $totalItems     = $stats ? $stats->total_items : 0;
        $remainingItems = $userMaxItems - $totalItems;

        
        $this->stats->update([
            'total_messages' => $totalMessages,
            'remaining_reviews' => $remainingItems,
            'total_cost_current_month' => $totalCostCurrentMonth,
        ]);
    }
}