<?php
namespace App\Services\Stats;

use App\Models\Item;
use App\Models\Message;
use App\Models\Order;
use App\Models\Review;
use App\Models\Stats;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Model;

class StatsService
{
    public function execute(Model $model)
    {
        try {
            $user = $model->user;

            if (! $user) {
                throw new Exception('User not found.');
            }

            $userId = $user->id;
            $stats  = Stats::where('user_id', $userId)
                ->where('year', date('Y'))
                ->where('month', date('m'))
                ->first();

            if (! $stats) {
                $stats          = new Stats();
                $stats->user_id = $userId;
                $stats->year    = date('Y');
                $stats->month   = date('m');
                $stats->save();
            }

            match (get_class($model)) {
                Order::class =>(new OrderStatsService($stats))->execute($model),
                Review::class =>(new ReviewStatsService($stats))->execute($model),
                Message::class =>(new MessageStatsService($stats))->execute($model),
                Item::class =>(new ItemStatsService($stats))->execute($model),
            };
        } catch (\Throwable $e) {
            Log::error('StatsService Error: ' . $e->getMessage(), [
                'model_class' => get_class($model),
                'trace'       => $e->getTraceAsString(),
            ]);
            // Optionally rethrow or handle gracefully
            throw $e;
        }
    }
}
