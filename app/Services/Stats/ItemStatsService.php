<?php

namespace App\Services\Stats;

use Exception;
use App\Models\Item;
use App\Models\Order;
use Illuminate\Database\Eloquent\Model;

class ItemStatsService extends AbstractStats
{

    public function execute(Item|Model $model)
    {
        $user = $model->user;

        if (! $user) {
            throw new Exception('User not found.');
        }

        $userId = $user->id;
        $year  = date('Y');
        $month = date('m');

        // Count items with message_sent = 1 for current month
        $itemCount = Item::where('user_id', $userId)
            ->where('message_sent', 1)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->count();

        // Count orders with is_message_sent = 1 but no message_sent items
        $orderCount = Order::where('user_id', $userId)
            ->where('is_message_sent', 1)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->whereDoesntHave('items', function ($query) {
                $query->where('message_sent', 1);
            })
            ->count();

        $totalReviewUsage = $itemCount + $orderCount;

        $this->stats->update([
           'total_reviews' => $totalReviewUsage,
        ]);

    }
}