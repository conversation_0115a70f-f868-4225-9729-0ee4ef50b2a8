<?php

namespace App\Services\Stats;

use App\Models\Order;
use Illuminate\Database\Eloquent\Model;

class OrderStatsService extends AbstractStats
{
   public function execute(Model|Order $model)
    {
        $userId = $model->user_id;
        $year =  date('Y');
        $month = date('m');
        $orders = Order::withCount('items')
            ->where('user_id', $userId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->get();
       
        $this->stats->update([
            'total_orders' => $orders->count() ?? 0,
            'total_items' => $orders->sum('items_count') ?? 0,
        ]);
    }
}