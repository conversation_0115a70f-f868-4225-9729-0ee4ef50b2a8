<?php

namespace App\Services\Shops\Shopify;

use App\Models\User;

class LocalManageService
{
    public static function execute(User $shop)
    {
        $locale = request()->get('locale') ?? null;
        if($locale === null) {
            $locale = $shop->locale ?? config('app.locale');
            app()->setLocale($locale);
            return; // No need to update if no locale is provided
        }

        // Check if the locale is already set
        if ($shop->locale === $locale) {
            app()->setLocale($locale);
            return; // No need to update if the locale is the same
        }

        //check if local exist in locales
        if (!in_array($locale, config('app.locales', []))) {
            return;
        }

        $user = User::find($shop->id);
        $user->locale = $locale;
        $user->save();

        // Set the application locale
        app()->setLocale($locale);
    }
}