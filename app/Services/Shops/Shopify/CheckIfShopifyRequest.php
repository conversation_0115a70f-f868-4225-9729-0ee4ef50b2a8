<?php

namespace App\Services\Shops\Shopify;

use App\Services\AppSettings;
use Assert\AssertionFailedException;
use Illuminate\Support\Facades\Cache;
use Osiset\ShopifyApp\Objects\Values\SessionToken;

class CheckIfShopifyRequest
{
    private static ?bool $shopify = null;
    private static ?string $locale = null;

    public static function execute(): bool
    {
        if (self::$shopify !== null) {

            if(self::$locale !== null) {
                app()->setLocale(self::$locale);
            }

            AppSettings::setCustomNotificationStatus(self::$shopify);
            return self::$shopify;
        }

        $request = request();
        $shop = $request->get('shop');
        $token = $request->get('token');
        $locale = $request->get('locale');
        $referer_url = $request->headers->get('referer');
        $referer_token = $referer_url ? isset(explode('id_token=', $referer_url)[1]) : null;
        $cookie = $request->cookie('shopify');
        $is_shopify = !empty($shop) || !empty($token) || !empty($referer_token) || !empty($cookie);
        $barearToken = $request->bearerToken();
        $appLocales = config('app.locales', []);

        if(!empty($locale) && in_array($locale, $appLocales)) {
            self::$locale = $locale;
            app()->setLocale($locale);
        }

        if ($barearToken && !$is_shopify) {
            try {
                SessionToken::fromNative($barearToken);
                $is_shopify = true;
            } catch (AssertionFailedException $e) {
                $is_shopify = false;
            }
        }

        if(!$is_shopify) {
            $cookieToken = Cache::get('shopify_custom_token');
            if ($cookieToken) {
                $is_shopify = true;
                request()->merge(['token' => $cookieToken]);
            }
        }

        self::$shopify = $is_shopify;

        if($is_shopify) {
            AppSettings::setCustomNotificationStatus(true);
        }

        return self::$shopify;
    }
}