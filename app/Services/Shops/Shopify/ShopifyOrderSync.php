<?php

namespace App\Services\Shops\Shopify;

use App\Models\Order;
use App\Models\User;
use Carbon\Carbon;

class ShopifyOrderSync
{
    /**
     * Sync all orders for a given shop.
     *
     * @param User $shop
     * @return bool
     */
    public function syncOrders(User $shop): bool
    {
        $lastSyncedAt = Order::where('user_id', $shop->id)
            ->latest('order_created_at')
            ->value('order_created_at');

        $lastMonth = Carbon::now()->subMonth()->toDateString();
        $lastSyncedAt = Carbon::parse($lastSyncedAt ?? $lastMonth)->toDateString();

        $after = null;

        $query = $this->buildQuery($lastSyncedAt, $after);
        $response = $shop->api()->graph($query);

        $orders = $response['body']['data']['orders'] ?? [];
        $edges = $orders['edges'] ?? [];
        $ordersToInsert = [];
        $orderItems = [];

        foreach ($edges as $edge) {
            $order = $edge['node'] ?? [];
            if (empty($order)) {
                continue;
            }
            $ordersToInsert[] = [
                'user_id' => $shop->id,
                'order_number' => $order['id'],
                'status' => $order['displayFinancialStatus'] ?? null,
                'total_amount' => $order['totalPriceSet']['shopMoney']['amount'] ?? null,
                'currency' => $order['totalPriceSet']['shopMoney']['currencyCode'] ?? null,
                'payment_method' => $order['paymentGatewayNames'][0] ?? null,
                'shipping_address' => isset($order['shippingAddress'])
                    ? $this->formatAddress($order['shippingAddress']['container'])
                    : null,
                'billing_address' => isset($order['billingAddress'])
                    ? $this->formatAddress($order['billingAddress']['container'])
                    : null,
                'shipping_country' => isset($order['shippingAddress']) ? $order['shippingAddress']['container']['countryCode'] : null,
                'billing_country'  => isset($order['billingAddress'])  ? $order['billingAddress']['container']['countryCode'] : null,
                'order_created_at' => Carbon::parse($order['createdAt']),
                'customer_name' => $order['customer']['firstName'] . ' ' . $order['customer']['lastName'],
                'customer_email' => $order['customer']['email'] ?? null,
                'phone_number' => $order['customer']['phone'] ?? $order['shippingAddress']['container']['phone'],
                'created_at' => now(),
                'updated_at' => now(),
            ];

            $orderItems[] = [
                'order_number' => $order['id'],
                'items' => $order['lineItems']['edges'] ?? [],
            ];
        }

        if ($ordersToInsert) {
            //Use updateOrCreate to ensure Observer fires
            foreach ($ordersToInsert as $orderData) {
                $order = Order::updateOrCreate(
                    [
                        'user_id'      => $orderData['user_id'],
                        'order_number' => $orderData['order_number'],
                    ],
                    $orderData
                );
            }

            foreach ($orderItems as $items) {
                $order = Order::where('order_number', $items['order_number'])->first();
                if ($order) {
                    foreach ($items['items'] as $item) {
                        $productId = $item['node']['product']['id'];
                        $order->items()->updateOrCreate(
                            [
                                'order_id'   => $order->id,
                                'product_id' => $productId,
                            ],
                            [
                                'name'       => $item['node']['title'],
                                'sku'        => $item['node']['sku'],
                                'price'      => $item['node']['variant']['price'],
                                'quantity'   => $item['node']['quantity'],
                                'variant_id' => $item['node']['variant']['id'] ?? null,
                                'img_url'    => $item['node']['product']['media']['edges'][0]['node']['image']['url'] ?? null,
                                'url'        => $item['node']['product']['onlineStoreUrl'] ?? null,
                                'user_id'    => $shop->id,
                            ]
                        );
                    }
                }
            }

        }

        return $orders['pageInfo']['hasNextPage'] ?? false;
    }

    /**
     * @param string $lastSyncedAt
     * @param string|null $after
     * @return string
     */
    private function buildQuery(string $lastSyncedAt, ?string $after = null): string
    {
        $cursorPart = $after ? ", after: \"{$after}\"" : "";

        return <<<GRAPHQL
    {
        orders(first: 10, query: "updated_at:>={$lastSyncedAt}"{$cursorPart}) {
            pageInfo {
                hasNextPage
            }
            edges {
                cursor
                node {
                    id
                    name
                    createdAt
                    displayFinancialStatus
                    paymentGatewayNames
                    shippingAddress {
                        address1
                        address2
                        city
                        province
                        zip
                        country
                        countryCode
                        phone
                    }
                    billingAddress {
                        address1
                        address2
                        city
                        province
                        zip
                        country
                        countryCode
                        phone
                    }
                    totalPriceSet {
                        shopMoney {
                            amount
                            currencyCode
                        }
                    }
                    customer {
                        firstName
                        lastName
                        email
                        phone
                    }
                    lineItems(first: 100) {
                        edges {
                            node {
                                id
                                title
                                quantity
                                sku
                                product { 
                                    id 
                                    onlineStoreUrl
                                    media(first: 1) {
                                        edges {
                                            node {
                                                ... on MediaImage {
                                                    image {
                                                        url
                                                        altText
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                variant {
                                    id
                                    title
                                    price
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    GRAPHQL;
    }

    /**
     * @param array $address
     * @return string
     */
    private function formatAddress(array $address): string
    {
        return trim(implode(', ', array_filter([
            $address['address1'] ?? null,
            $address['address2'] ?? null,
            $address['city'] ?? null,
            $address['province'] ?? null,
            $address['zip'] ?? null,
            $address['country'] ?? null,
            $address['phone'] ?? null,
        ])));
    }

}