<?php

namespace App\Services\Shops\Shopify;

use App\Models\User;
use Osiset\ShopifyApp\Contracts\Objects\Values\AccessToken as AccessTokenValue;
use Osiset\ShopifyApp\Contracts\Objects\Values\ShopId as ShopIdValue;
use Osiset\ShopifyApp\Storage\Commands\Shop as ShopCommand;

class ShopManager extends ShopCommand
{
    /**
     * {@inheritdoc}
     */
    public function setAccessToken(ShopIdValue $shopId, AccessTokenValue $token): bool
    {
        /** @var User $shop */
        $shop = $this->getShop($shopId);
        $shop->settings()->set('shopify.offline_access_token', $token->toNative());
        $shop->save();

        return parent::setAccessToken($shopId, $token);
    }
}