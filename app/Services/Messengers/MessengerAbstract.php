<?php

namespace App\Services\Messengers;

use App\Enums\MessageStatus;
use App\Events\MessageSent;
use App\Models\Message;
use App\Services\User\Balance\BeforeMessageSend;
use Filament\Notifications\Notification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

abstract class MessengerAbstract
{
    protected Message $message;

    protected BeforeMessageSend $beforeMessageSendService;
    protected NumberValidation $numberValidation;

    public function __construct()
    {
        $this->beforeMessageSendService = new BeforeMessageSend();
        $this->numberValidation = new NumberValidation();
    }

    protected $templateId = null;
    abstract protected function sendMessage(string $message, string $phone);
    abstract public function setRate();
    abstract public function setAsk();
    abstract public function parseMessageFromRequest(Request $request): bool;

    public function send(Message $message)
    {
        try {
            if(!$this->beforeMessageSendService->execute($message)) {
                Notification::make()
                    ->title(__('messages.notifications.messages.insufficient_balance'))
                    ->danger()
                    ->sendToDatabase($message->user);
                $message->status = MessageStatus::INSUFFICIENT_BALANCE->value;
                $message->save();
                throw new \Exception(__('Insufficient balance'));
            }

            if(!$phoneNumber = $this->numberValidation->execute($message)) {
                Notification::make()
                    ->title(__('messages.notifications.messages.invalid_phone_number'))
                    ->danger()
                    ->sendToDatabase($message->user);
                $message->status = MessageStatus::INVALID_PHONE_NUMBER->value;
                $message->save();
                throw new \Exception(__('Invalid phone number'));
            }

            if($this->sendMessage($message->message, $phoneNumber)) {
                $message->status = MessageStatus::SENT->value;
                $message->save();
                event(new MessageSent($message));
            } else {
                Notification::make()
                    ->title(__('messages.notifications.messages.failed_to_send'))
                    ->danger()
                    ->sendToDatabase($message->user);
                $message->status = MessageStatus::FAILED->value;
                $message->save();
            }
        }
        catch (\Exception $e) {
            Log::error('Error sending message: ' . $e->getMessage() . ' - Message ID: ' . $message->id . '- trace : ' . $e->getTraceAsString());
        }
    }

    protected function sanitizeForTemplate(string $text): string
    {
        // Remove any newline characters
        $text = str_replace(["\r", "\n"], ' ', $text);

        // Collapse multiple spaces into one
        $text = preg_replace('/\s+/', ' ', $text);

        // Trim leading and trailing whitespace
        $text = trim($text);

        return $text;
    }
}