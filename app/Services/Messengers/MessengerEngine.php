<?php

namespace App\Services\Messengers;

class MessengerEngine
{
    public static function getActiveMessenger()
    {
        $messenger = config('services.messenger');
        if ($messenger === 'meta') {
            return new \App\Services\Messengers\Whatsapp\MetaMessenger();
        } elseif ($messenger === 'twilio') {
            return new \App\Services\Messengers\Whatsapp\TwilioMessenger();
        } else {
            throw new \Exception('Invalid messenger configuration');
        }
    }
}