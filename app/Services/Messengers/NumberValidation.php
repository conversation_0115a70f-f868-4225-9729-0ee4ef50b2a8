<?php

namespace App\Services\Messengers;

use App\Models\Message;
use App\Services\PhoneNumberService;

class NumberValidation
{
    private PhoneNumberService $phoneNumberService;
    public function __construct()
    {
        $this->phoneNumberService = new PhoneNumberService();
    }

    public function execute(Message $message): ?string
    {
        $phoneNumber = $message->phone_number;
        $countryCode = $message->order->shipping_country;
        $billingCountryCode = $message->order->billing_country;

        if(!$countryCode && !$billingCountryCode) {
            return $phoneNumber;
        }

        return $this->phoneNumberService->formatWithIso($phoneNumber, $countryCode ?? $billingCountryCode);
    }
}