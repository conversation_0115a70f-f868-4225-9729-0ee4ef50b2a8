<?php

namespace App\Services\Messengers;

use App\Enums\ConversationMessageTypes as MsgType;
use App\Enums\SenderTypes;
use App\Enums\Settings;
use App\Models\Item as OrderItem;
use App\Models\Message;
use App\Models\MessageTemplates;
use App\Models\Order;
use App\Services\Messengers\MessengerAbstract as Messenger;
use App\Services\User\GetBlackListedSkus;

class ConversationGenerator
{
    public function __construct(protected Messenger $messenger) {}

    public function execute(Order $order): void
    {
        $messages = $order->messages()->success()->orderBy('created_at')->get();
        $user = $order->user;
        $phone = $order->phone_number;
        $lastCustomerMessage = $messages->where('sender', SenderTypes::CUSTOMER)->last();
        $lastSystemMessage = $messages->where('sender', SenderTypes::SYSTEM)->last();
        $lastSentMessagetoNumber = Message::where('phone_number', $phone)->orderByDesc('created_at')->limit(1)->first();

        //$lastSentMessagetoNumber is sent in the last 24 hours, so we don't send it again
        if ($lastSentMessagetoNumber && $lastSentMessagetoNumber->sender == SenderTypes::SYSTEM->value && $lastSentMessagetoNumber->created_at->diffInHours(now()) < 24) {
            throw new \RuntimeException('A message has already been sent to this number in the last 24 hours.');
        }

        if (!$messages->count()) {
            $template = $this->getTemplate($user->id, MsgType::STARTER_MESSAGE);
            $template->setOrder($order)->generateMessage($order);
            $this->applyTemplateConfig($template->type);
            $this->messenger->send($template->message);
            return;
        }

        $item = $this->getCurrentItem($order);
        if (!$item) {
            $this->sendThankYou($order);
            return;
        }

        // ÖZEL DURUM: starter mesajından sonra gelen YES mesajı süreci başlatmalıdır
        if ($lastSystemMessage->type == MsgType::STARTER_MESSAGE->value && $this->isAfter($lastSystemMessage, $lastCustomerMessage) && $this->isYes($lastCustomerMessage->message)) {
            $this->sendRateAsk($user, $order, $item);
            return;
        }

        // Standart kontrol: son sistem mesajı varsa ve son müşteri mesajı sonrasında geldiyse işle
        if (!$lastSystemMessage || !$lastCustomerMessage || !$this->isAfter($lastSystemMessage, $lastCustomerMessage)) {
            return;
        }

        $contextType = $lastSystemMessage->type;

        switch ($contextType) {
            case MsgType::RATE_ASK_MESSAGE->value:
                if ($this->isRate($lastCustomerMessage->message)) {
                    $rating = (int) $lastCustomerMessage->message;
                    $item->review()->updateOrCreate(
                        ['order_id' => $order->id],
                        ['user_id' => $user->id, 'rating' => $rating]
                    );

                    if($order->user->getSetting(Settings::ASK_FOR_COMMENT->value)) {
                        $this->askCommentOffer($user, $order, $item);
                    } else {
                        $this->finalizeItem($order, $item);
                    }
                }
                break;

            case MsgType::COMMENT_OFFER_MESSAGE->value:
                if ($this->isYes($lastCustomerMessage->message)) {
                    $template = $this->getTemplate($user->id, MsgType::REVIEW_ASK_MESSAGE);
                    $template->setOrder($order)->generateMessage($item);
                    $this->applyTemplateConfig($template->type);
                    $this->messenger->send($template->message);
                } else {
                    $this->finalizeItem($order, $item);
                    $item->review()->update(['done' => true]);
                }
                break;

            case MsgType::REVIEW_ASK_MESSAGE->value:
                $item->review()->update([
                    'review_description' => $lastCustomerMessage->message,
                    'done' => true,
                ]);
                $this->finalizeItem($order, $item);
                break;
        }
    }

    protected function sendRateAsk($user, $order, $item): void
    {
        $template = $this->getTemplate($user->id, MsgType::RATE_ASK_MESSAGE);
        $template->setOrder($order)->generateMessage($item);
        $this->applyTemplateConfig($template->type);
        $this->messenger->send($template->message);
    }

    protected function finalizeItem(Order $order, OrderItem $item): void
    {
        $item->update(['has_review' => true]);
        $review = $item->review;
        $review->update(['done' => true]);
        $template = $this->getTemplate($order->user_id, MsgType::REVIEW_THANK_MESSAGE);
        $template->setOrder($order)->generateMessage($item);
        $this->applyTemplateConfig($template->type);
        $this->messenger->send($template->message);
        if ($this->allItemsReviewed($order)) {
            $this->sendThankYou($order);
        } else {
            $next = $this->getCurrentItem($order);
            if ($next) $this->sendRateAsk($order->user, $order, $next);
        }
    }

    protected function askCommentOffer($user, $order, $item)
    {
        $template = $this->getTemplate($user->id, MsgType::COMMENT_OFFER_MESSAGE);
        $template->setOrder($order)->generateMessage($item);
        $this->applyTemplateConfig($template->type);
        $this->messenger->send($template->message);
    }

    protected function getCurrentItem(Order $order): ?OrderItem
    {
        $items = $order->items->where('has_review', false);
        $blackListedSkus = GetBlackListedSkus::execute($order->user);

        foreach ($items as $item) {
            if (!in_array($item->sku, $blackListedSkus)) {
                return $item;
            }
        }

        return null;
    }

    protected function getTemplate(int $userId, MsgType $type): MessageTemplates
    {
        return MessageTemplates::where('user_id', $userId)
            ->where('type', $type->value)
            ->where('is_default', true)
            ->firstOrFail();
    }

    protected function isAfter(?Message $systemMsg, ?Message $customerMsg): bool
    {
        return $systemMsg && $customerMsg && $customerMsg->created_at->gt($systemMsg->created_at);
    }

    protected function isYes(?string $msg): bool
    {
        return in_array(strtolower(trim($msg ?? '')), ['yes', 'evet']);
    }

    protected function isNo(?string $msg): bool
    {
        return in_array(strtolower(trim($msg ?? '')), ['no', 'hayır', 'yok']);
    }

    protected function isRate(?string $msg): bool
    {
        return in_array((int) ($msg ?? 0), [1, 2, 3, 4, 5]);
    }

    protected function sendThankYou(Order $order): void
    {
        if (!$order->messages->where('type', MsgType::THANK_YOU_MESSAGE->value)->count()) {
            $template = $this->getTemplate($order->user_id, MsgType::THANK_YOU_MESSAGE);
            $template->setOrder($order)->generateMessage($order);
            $this->applyTemplateConfig($template->type);
            $this->messenger->send($template->message);
        }
    }

    protected function allItemsReviewed(Order $order): bool
    {
        return $order->items->every(fn($item) => $item->has_review);
    }

    protected function applyTemplateConfig(string $type): void
    {
        match ($type) {
            MsgType::RATE_ASK_MESSAGE->value     => $this->messenger->setRate(),
            MsgType::COMMENT_OFFER_MESSAGE->value,
            MsgType::RATE_OFFER_MESSAGE->value,
            MsgType::STARTER_MESSAGE->value      => $this->messenger->setAsk(),
            default                              => null,
        };
    }
}
