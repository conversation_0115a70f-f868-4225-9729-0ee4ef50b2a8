<?php

namespace App\Services\Messengers\Whatsapp;

use App\Enums\SenderTypes;
use App\Jobs\SendMessageJob;
use App\Models\Message;
use App\Services\Messengers\MessengerAbstract;
use Illuminate\Support\Facades\Log;

class TwilioMessenger extends MessengerAbstract
{
    public function setAsk()
    {
        $this->templateId = config('services.twilio.ask_template_id');
        return $this;
    }

    public function setRate()
    {
        $this->templateId = config('services.twilio.rate_template_id');
        return $this;
    }

    public function sendMessage(string $message, string $phone)
    {
        $sid = config('services.twilio.sid');
        $token = config('services.twilio.token');
        $twilio = new \Twilio\Rest\Client($sid, $token);
        $variables = [
            'from' => "whatsapp:" . config('services.twilio.from'),
        ];

        if($this->templateId) {
            $variables['contentSid'] = $this->templateId;
            $variables['contentVariables'] = json_encode([
                "1" => $this->sanitizeForTemplate($message),
            ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        } else {
            $variables['body'] = $message;
        }

        $variables['messagingServiceSid'] = config('services.twilio.messaging_service_sid');

        $message = $twilio->messages
            ->create("whatsapp:$phone", $variables);
        if ($message->sid) {
            Log::info('Message sent successfully. SID: ' . $message->sid);
        } else {
            Log::error('Failed to send message.');
        }

        return $message->sid ?? null;
    }

    public function parseMessageFromRequest($request): bool
    {
        $data = $request->all();
        if(!isset($data['Body'])) {
            return true;
        }

        //extract the phone number From : whatsapp:+905377964105
        $phoneNumber = substr($data['From'], 9);
        $messageBody = $data['Body'];
        $message = Message::where('phone_number', $phoneNumber)->success()->latest()->first();
        $newMessage = new Message();
        $newMessage->fill($message->toArray());
        $newMessage->message = $messageBody;
        $newMessage->sender = SenderTypes::CUSTOMER->value;
        $newMessage->type = 'text';
        $newMessage->save();
        SendMessageJob::dispatch($message->order);

        return true;
    }
}