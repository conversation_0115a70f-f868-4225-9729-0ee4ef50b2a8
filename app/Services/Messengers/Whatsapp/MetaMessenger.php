<?php

namespace App\Services\Messengers\Whatsapp;

use App\Enums\SenderTypes;
use App\Jobs\SendMessageJob;
use App\Models\Message;
use App\Services\Messengers\MessengerAbstract;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Netflie\WhatsAppCloudApi\Message\OptionsList\Action;
use Netflie\WhatsAppCloudApi\Message\OptionsList\Row;
use Netflie\WhatsAppCloudApi\Message\OptionsList\Section;
use Netflie\WhatsAppCloudApi\Message\Template\Component;
use Netflie\WhatsAppCloudApi\WhatsAppCloudApi;

class MetaMessenger extends MessengerAbstract
{
    protected $whatsapp_cloud_api;
    private $buttons = [];
    private $isRate = false;
    public function __construct()
    {
        $this->whatsapp_cloud_api = new WhatsAppCloudApi([
            'from_phone_number_id' => config('services.meta.number_id'),
            'access_token' => config('services.meta.token'),
        ]);

        parent::__construct();
    }

    public function setAsk()
    {
        $this->templateId = config('services.meta.ask_template_id');
        return $this;
    }

    public function setRate()
    {
        $this->buttons = [[
            "type" => "button",
            "sub_type" => "flow",
             "index" =>  0
        ]];
        $this->isRate = true;
        $this->templateId = config('services.meta.rate_template_id');
        return $this;
    }

    protected function sendMessage(string $message, string $phone)
    {
        try {

            if($this->isRate && !$this->templateId) {
                $this->sendSimpleRateTemplate($phone, $message);
                return true;
            }

            if (!$this->templateId) {
                $response = $this->whatsapp_cloud_api->sendTextMessage($phone, $message);
            } else {
                $message = $this->sanitizeForTemplate($message);

                $component_body = [
                    [
                        'type' => 'text',
                        'text' => $message
                    ],
                ];
                $component = new Component([], $component_body, $this->buttons);
                $response = $this->whatsapp_cloud_api->sendTemplate(
                    $phone,
                    $this->templateId,
                    'en',
                    $component
                );
            }

            if ($response->httpStatusCode() !== 200) {
                Log::error('Failed to send template message: ' . $response->body());
                return false;
            }

            return true;

        } catch (\Exception $e) {
            Log::error('Error sending message: ' . $e->getMessage() . ' - Message ID: ' . $this->message->id . '- trace : ' . $e->getTraceAsString());
            return false;
        }
    }

    public function parseMessageFromRequest($request): bool
    {
        $data = $request->all();

        if(!isset($data['entry'][0]['changes'][0]['value']['contacts'][0]['wa_id'])) {
            return true;
        }

        $value = $data['entry'][0]['changes'][0]['value'];
        $phone   = $value['contacts'][0]['wa_id'];

        $messageId = $value['messages'][0]['id'];
        if(Cache::get($messageId)) {
            return true;
        }

        Cache::put($messageId, true, 60);

        if (
            isset($value['messages'][0]['interactive']['list_reply']['id'])
        ) {
            $messageBody = $value['messages'][0]['interactive']['list_reply']['id'];
        } else if(
            isset($value['messages'][0]['button']['text'])
        ) {
            $messageBody = $value['messages'][0]['button']['text'];
        }
        else {
            $messageBody = $value['messages'][0]['text']['body'];
        }

        if(!$messageBody || !$phone) {
            return true;
        }

        //extract the phone number From : whatsapp:+905377964105
        $phoneNumber = '+' . $phone;
        $message = Message::where('phone_number', $phoneNumber)->success()->latest()->first();
        $newMessage = new Message();
        $newMessage->fill($message->toArray());
        $newMessage->message = $messageBody;
        $newMessage->sender = SenderTypes::CUSTOMER->value;
        $newMessage->type = 'text';
        $newMessage->save();
        SendMessageJob::dispatch($message->order);

        return true;
    }

    private function interactiveParser($data)
    {
        $message = $data['entry'][0]['changes'][0]['value']['messages'][0]
            ?? null;

        if (!$message) {
            // no message found
            http_response_code(400);
            exit('No message payload');
        }

        // Check if it's an interactive message
        if ($message['type'] === 'interactive') {
            $interactive = $message['interactive'];

            // BUTTON_REPLY (quick reply buttons)
            if (isset($interactive['button_reply'])) {
                $selectedId = $interactive['button_reply']['id'];
                $selectedTitle = $interactive['button_reply']['title'];
                // e.g. $selectedId = "option_1", $selectedTitle = "Yes"

                // LIST_REPLY (list picker)
            } elseif (isset($interactive['list_reply'])) {
                $selectedId = $interactive['list_reply']['id'];
                $selectedTitle = $interactive['list_reply']['title'];
                // e.g. $selectedId = "item_3", $selectedTitle = "Green Sneakers"

                // NFM_REPLY (new rich‐form flow)
            } elseif (isset($interactive['nfm_reply'])) {
                // The payload is a JSON‐encoded string
                $nfm = $interactive['nfm_reply'];
                $responseJson = json_decode($nfm['response_json'], true);

                // Free‐form text input
                $freeText = $responseJson['screen_0_Leave_a_1']
                    ?? null;

                // The selected choice comes as "<index>_<emojiString>"
                $rawChoice = $responseJson['screen_0_Choose_one_0']
                    ?? null;

                if ($rawChoice !== null) {
                    // Split into the numeric index and the rest of the string
                    list($selectedIndex, $selectedValue) = explode('_', $rawChoice, 2);
                    // e.g. $selectedIndex = "4", $selectedValue = "⭐⭐⭐⭐⭐"
                }

                // Now you can use:
                //   $freeText
                //   $selectedIndex (as string or cast to int)
                //   $selectedValue (the star‐string)
            }

            // Example: log what we found
            error_log('Interactive type: ' . $interactive['type']);
            if (isset($selectedIndex)) {
                error_log("Selected index: $selectedIndex, value: $selectedValue");
            }
            if (isset($freeText)) {
                error_log("User input: $freeText");
            }
        } else {
            // Non‐interactive message
            // handle text, image, etc.
            $text = $message['text']['body'] ?? '';
            error_log("Standard text message: $text");
        }
    }

    private function sendSimpleRateTemplate($phone, $message)
    {
        $button = 'Rate';
        $section = new Section('Rate', [
            new Row('1', '⭐'),
            new Row('2', '⭐⭐'),
            new Row('3', '⭐⭐⭐'),
            new Row('4', '⭐⭐⭐⭐'),
            new Row('5', '⭐⭐⭐⭐⭐'),
        ]);
        $action = new Action($button, [$section]);
        $this->whatsapp_cloud_api->sendList(
            $phone,
            '',
            $message,
            '',
            $action
        );
    }
}