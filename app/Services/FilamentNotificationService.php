<?php

namespace App\Services;

use Filament\Notifications\Notification;
use Livewire\Features\SupportEvents\Event;
use Livewire\Features\SupportEvents\HandlesEvents;
use function Livewire\store;

class FilamentNotificationService extends Notification
{
    use HandlesEvents;

    public function send(): static
    {
        if (!AppSettings::getCustomNotificationStatus()) {
            return parent::send();
        }

        $event = new Event('customNotification', $this->toArray());
        $backtrace = debug_backtrace();

        for ($i = 1; $i < count($backtrace); $i++) {
            if (
                array_key_exists('object', $backtrace[$i]) &&
                $backtrace[$i]['object'] !== null &&
                is_a($backtrace[$i]['object'], 'Livewire\Component')
            ) {
                break;
            }
        }

        store($backtrace[$i]['object'])->push('dispatched', $event);

        return $this;
    }
}