<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Validering <PERSON>
    |--------------------------------------------------------------------------
    |
    | De følgende språklinjene inneholder standard feilmeldingene som brukes
    | av validatorklassen. Noen av disse reglene har flere versjoner, som
    | størrelsesreglene. Du kan justere hver av disse meldingene her.
    |
    */

    'accepted' => ':attribute feltet må aksepteres.',
    'accepted_if' => ':attribute feltet må aksepteres når :other er :value.',
    'active_url' => ':attribute feltet må være en gyldig URL.',
    'after' => ':attribute feltet må være en dato etter :date.',
    'after_or_equal' => ':attribute feltet må være en dato etter eller lik :date.',
    'alpha' => ':attribute feltet må kun inneholde bokstaver.',
    'alpha_dash' => ':attribute feltet må kun inneholde bokstaver, tall, bindestreker og understreker.',
    'alpha_num' => ':attribute feltet må kun inneholde bokstaver og tall.',
    'any_of' => ':attribute feltet er ugyldig.',
    'array' => ':attribute feltet må være en array.',
    'ascii' => ':attribute feltet må kun inneholde enkelt-byte alfanumeriske tegn og symboler.',
    'before' => ':attribute feltet må være en dato før :date.',
    'before_or_equal' => ':attribute feltet må være en dato før eller lik :date.',
    'between' => [
        'array' => ':attribute feltet må ha mellom :min og :max elementer.',
        'file' => ':attribute feltet må være mellom :min og :max kilobytes.',
        'numeric' => ':attribute feltet må være mellom :min og :max.',
        'string' => ':attribute feltet må være mellom :min og :max tegn.',
    ],
    'boolean' => ':attribute feltet må være sant eller usant.',
    'can' => ':attribute feltet inneholder en uautorisert verdi.',
    'confirmed' => ':attribute feltbekreftelsen stemmer ikke overens.',
    'contains' => ':attribute feltet mangler en påkrevd verdi.',
    'current_password' => 'Passordet er feil.',
    'date' => ':attribute feltet må være en gyldig dato.',
    'date_equals' => ':attribute feltet må være en dato lik :date.',
    'date_format' => ':attribute feltet må samsvare med formatet :format.',
    'decimal' => ':attribute feltet må ha :decimal desimalplasser.',
    'declined' => ':attribute feltet må avslås.',
    'declined_if' => ':attribute feltet må avslås når :other er :value.',
    'different' => ':attribute feltet og :other må være forskjellige.',
    'digits' => ':attribute feltet må være :digits siffer.',
    'digits_between' => ':attribute feltet må være mellom :min og :max siffer.',
    'dimensions' => ':attribute feltet har ugyldige bildedimensjoner.',
    'distinct' => ':attribute feltet har en duplikatverdi.',
    'doesnt_end_with' => ':attribute feltet må ikke slutte med en av følgende: :values.',
    'doesnt_start_with' => ':attribute feltet må ikke starte med en av følgende: :values.',
    'email' => ':attribute feltet må være en gyldig e-postadresse.',
    'ends_with' => ':attribute feltet må slutte med en av følgende: :values.',
    'enum' => 'Den valgte :attribute er ugyldig.',
    'exists' => 'Den valgte :attribute er ugyldig.',
    'extensions' => ':attribute feltet må ha en av følgende filtyper: :values.',
    'file' => ':attribute feltet må være en fil.',
    'filled' => ':attribute feltet må ha en verdi.',
    'gt' => [
        'array' => ':attribute feltet må ha mer enn :value elementer.',
        'file' => ':attribute feltet må være større enn :value kilobytes.',
        'numeric' => ':attribute feltet må være større enn :value.',
        'string' => ':attribute feltet må være større enn :value tegn.',
    ],
    'gte' => [
        'array' => ':attribute feltet må ha :value elementer eller mer.',
        'file' => ':attribute feltet må være større enn eller lik :value kilobytes.',
        'numeric' => ':attribute feltet må være større enn eller lik :value.',
        'string' => ':attribute feltet må være større enn eller lik :value tegn.',
    ],
    'hex_color' => ':attribute feltet må være en gyldig heksadesimal farge.',
    'image' => ':attribute feltet må være et bilde.',
    'in' => 'Den valgte :attribute er ugyldig.',
    'in_array' => ':attribute feltet må eksistere i :other.',
    'integer' => ':attribute feltet må være et heltall.',
    'ip' => ':attribute feltet må være en gyldig IP-adresse.',
    'ipv4' => ':attribute feltet må være en gyldig IPv4-adresse.',
    'ipv6' => ':attribute feltet må være en gyldig IPv6-adresse.',
    'json' => ':attribute feltet må være en gyldig JSON-streng.',
    'list' => ':attribute feltet må være en liste.',
    'lowercase' => ':attribute feltet må være små bokstaver.',
    'lt' => [
        'array' => ':attribute feltet må ha mindre enn :value elementer.',
        'file' => ':attribute feltet må være mindre enn :value kilobytes.',
        'numeric' => ':attribute feltet må være mindre enn :value.',
        'string' => ':attribute feltet må være mindre enn :value tegn.',
    ],
    'lte' => [
        'array' => ':attribute feltet må ikke ha mer enn :value elementer.',
        'file' => ':attribute feltet må være mindre enn eller lik :value kilobytes.',
        'numeric' => ':attribute feltet må være mindre enn eller lik :value.',
        'string' => ':attribute feltet må være mindre enn eller lik :value tegn.',
    ],
    'mac_address' => ':attribute feltet må være en gyldig MAC-adresse.',
    'max' => [
        'array' => ':attribute feltet må ikke ha mer enn :max elementer.',
        'file' => ':attribute feltet må ikke være større enn :max kilobytes.',
        'numeric' => ':attribute feltet må ikke være større enn :max.',
        'string' => ':attribute feltet må ikke være større enn :max tegn.',
    ],
    'max_digits' => ':attribute feltet må ikke ha mer enn :max siffer.',
    'mimes' => ':attribute feltet må være en fil av typen: :values.',
    'mimetypes' => ':attribute feltet må være en fil av typen: :values.',
    'min' => [
        'array' => ':attribute feltet må ha minst :min elementer.',
        'file' => ':attribute feltet må være minst :min kilobytes.',
        'numeric' => ':attribute feltet må være minst :min.',
        'string' => ':attribute feltet må være minst :min tegn.',
    ],
    'min_digits' => ':attribute feltet må ha minst :min siffer.',
    'missing' => ':attribute feltet må mangle.',
    'missing_if' => ':attribute feltet må mangle når :other er :value.',
    'missing_unless' => ':attribute feltet må mangle med mindre :other er :value.',
    'missing_with' => ':attribute feltet må mangle når :values er tilstede.',
    'missing_with_all' => ':attribute feltet må mangle når :values er tilstede.',
    'multiple_of' => ':attribute feltet må være et multiplum av :value.',
    'not_in' => 'Den valgte :attribute er ugyldig.',
    'not_regex' => ':attribute feltformatet er ugyldig.',
    'numeric' => ':attribute feltet må være et tall.',
    'password' => [
        'letters' => ':attribute feltet må inneholde minst én bokstav.',
        'mixed' => ':attribute feltet må inneholde minst én stor og én liten bokstav.',
        'numbers' => ':attribute feltet må inneholde minst ett tall.',
        'symbols' => ':attribute feltet må inneholde minst ett symbol.',
        'uncompromised' => 'Det oppgitte :attribute har dukket opp i en datalekkasje. Velg et annet :attribute.',
    ],
    'present' => ':attribute feltet må være tilstede.',
    'present_if' => ':attribute feltet må være tilstede når :other er :value.',
    'present_unless' => ':attribute feltet må være tilstede med mindre :other er :value.',
    'present_with' => ':attribute feltet må være tilstede når :values er tilstede.',
    'present_with_all' => ':attribute feltet må være tilstede når :values er tilstede.',
    'prohibited' => ':attribute feltet er forbudt.',
    'prohibited_if' => ':attribute feltet er forbudt når :other er :value.',
    'prohibited_if_accepted' => ':attribute feltet er forbudt når :other er akseptert.',
    'prohibited_if_declined' => ':attribute feltet er forbudt når :other er avslått.',
    'prohibited_unless' => ':attribute feltet er forbudt med mindre :other er i :values.',
    'prohibits' => ':attribute feltet forbyr :other fra å være tilstede.',
    'regex' => ':attribute feltformatet er ugyldig.',
    'required' => ':attribute feltet er påkrevd.',
    'required_array_keys' => ':attribute feltet må inneholde oppføringer for: :values.',
    'required_if' => ':attribute feltet er påkrevd når :other er :value.',
    'required_if_accepted' => ':attribute feltet er påkrevd når :other er akseptert.',
    'required_if_declined' => ':attribute feltet er påkrevd når :other er avslått.',
    'required_unless' => ':attribute feltet er påkrevd med mindre :other er i :values.',
    'required_with' => ':attribute feltet er påkrevd når :values er tilstede.',
    'required_with_all' => ':attribute feltet er påkrevd når :values er tilstede.',
    'required_without' => ':attribute feltet er påkrevd når :values ikke er tilstede.',
    'required_without_all' => ':attribute feltet er påkrevd når ingen av :values er tilstede.',
    'same' => ':attribute feltet må samsvare med :other.',
    'size' => [
        'array' => ':attribute feltet må inneholde :size elementer.',
        'file' => ':attribute feltet må være :size kilobytes.',
        'numeric' => ':attribute feltet må være :size.',
        'string' => ':attribute feltet må være :size tegn.',
    ],
    'starts_with' => ':attribute feltet må starte med en av følgende: :values.',
    'string' => ':attribute feltet må være en streng.',
    'timezone' => ':attribute feltet må være en gyldig tidssone.',
    'unique' => ':attribute har allerede blitt tatt.',
    'uploaded' => ':attribute kunne ikke lastes opp.',
    'uppercase' => ':attribute feltet må være store bokstaver.',
    'url' => ':attribute feltet må være en gyldig URL.',
    'ulid' => ':attribute feltet må være en gyldig ULID.',
    'uuid' => ':attribute feltet må være en gyldig UUID.',

    /*
    |--------------------------------------------------------------------------
    | Tilpassede Valideringsspråklinjer
    |--------------------------------------------------------------------------
    |
    | Her kan du spesifisere tilpassede valideringsmeldinger for attributter ved å bruke
    | konvensjonen "attribute.rule" for å navngi linjene. Dette gjør det raskt å
    | spesifisere en spesifikk tilpasset språklinje for en gitt attributtregel.
    |
    */

    'custom' => [
        'attribute-name' => [
            'rule-name' => 'custom-message',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Tilpassede Valideringsattributter
    |--------------------------------------------------------------------------
    |
    | De følgende språklinjene brukes til å bytte ut attributtplassholdere
    | med noe mer lesevennlig som "E-postadresse" i stedet for "email".
    | Dette hjelper oss bare å gjøre meldingen vår mer uttrykksfullt.
    |
    */

    'attributes' => [],

];