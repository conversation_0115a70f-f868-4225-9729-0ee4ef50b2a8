<?php

use App\Http\Controllers\ShopifyComplianceWebhookController;
use App\Http\Controllers\WhatsAppController;
use Illuminate\Support\Facades\Route;

Route::prefix('/messenger')->group(function () {
    Route::post('/whatsapp/callback', [WhatsAppController::class, 'messageCallback'])
        ->name('messenger.whatsapp.callback');

    Route::get('/whatsapp/callback', [WhatsAppController::class, 'statusCallback'])
        ->name('messenger.whatsapp.callback.get');
});


Route::prefix('shopify/webhooks')->middleware(\App\Http\Middleware\VerifyShopifyWebhook::class)->group(function () {
    Route::post('customers/data_request', [ShopifyComplianceWebhookController::class, 'handleDataRequest']);
    Route::post('customers/redact', [ShopifyComplianceWebhookController::class, 'handleRedactRequest']);
    Route::post('shop/redact', [ShopifyComplianceWebhookController::class, 'handleShopRedactRequest']);
});