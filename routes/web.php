<?php

use App\Http\Controllers\Auth\JudgeMeController;
use App\Http\Controllers\PaytrController;
use App\Http\Controllers\PlanController;
use Illuminate\Support\Facades\Route;

Route::group(['excluded_middleware' => ['verify.scopes']], function () {
    Route::get('/', function () {
        return view('welcome');
    })->name('site.home');

    Route::get('/about', function () {
        return view('pages.about-us');
    })->name('site.about');

    Route::get('/privacy', function () {
        return view('pages.privacy-policy');
    })->name('site.privacy');

    Route::get('/terms', function () {
        return view('pages.terms-of-service');
    })->name('site.terms');

    Route::get('/compliance', function () {
        return view('pages.compliance');
    })->name('site.compliance');

    Route::get('/cookie', function () {
        return view('pages.cookie-policy');
    })->name('site.cookie');

    Route::get('/sale', function () {
        return view('pages.sale');
    })->name('site.sale');
});

Route::get('/home', function () {
    return view('welcome');
})->name('home');


Route::get('/login', function () {
    return view('welcome');
})->name('home');


Route::get('/plans/{plan}', [PlanController::class, 'show'])->name('plans.show');
Route::get('/checkout/{plan}', [PlanController::class, 'checkout'])->name('checkout');
Route::post('/checkout/{plan}', [PlanController::class, 'processCheckout'])->name('checkout.process');

Route::get('/setToken', function () {
    $token = request()->get('custom_token');
    \Illuminate\Support\Facades\Cache::put('shopify_custom_token', $token, now()->addMinutes(60));
    return response()->json(['message' => 'Token set successfully']);
})->name('setToken');


Route::prefix('/integrations')->group(function () {
    Route::get('/auth/judgeme/redirect', [JudgeMeController::class, 'redirectToProvider']);
    Route::get('/auth/judgeme/callback', [JudgeMeController::class, 'handleProviderCallback']);
});

Route::post('/paytr-token', [PaytrController::class, 'generateToken'])->middleware(\App\Http\Middleware\FilamentAuth::class)->name('paytr.token');
Route::get('/balance/topup/success', [PaytrController::class, 'success'])->middleware(\App\Http\Middleware\FilamentAuth::class)->name('balance.topup.success');
Route::get('/balance/topup/fail', [PaytrController::class, 'fail'])->middleware(\App\Http\Middleware\FilamentAuth::class)->name('balance.topup.fail');
