<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    'judgeme' => [
        'client_id' => env('JUDGEME_CLIENT_ID'),
        'client_secret' => env('JUDGEME_CLIENT_SECRET'),
        'redirect' => env('JUDGEME_REDIRECT_URI'),
    ],

    'messenger' => env('MESSENGER', 'twilio'),

    'twilio' => [
        'sid' => env('TWILIO_SID'),
        'token' => env('TWILIO_TOKEN'),
        'from' => env('TWILIO_FROM'),
        'ask_template_id' => env('TWILIO_ASK_TEMPLATE_ID'),
        'rate_template_id' => env('TWILIO_RATE_TEMPLATE_ID'),
        'messaging_service_sid' => env('TWILIO_MESSAGING_SERVICE_SID'),
    ],

    'meta' => [
        'token' => env('META_TOKEN'),
        'number_id' => env('META_NUMBER_ID'),
        'ask_template_id' => env('META_ASK_TEMPLATE_ID'),
        'rate_template_id' => env('META_RATE_TEMPLATE_ID', null),
    ]
];
