<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stats', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->integer('year')->nullable();
            $table->integer('month')->nullable();
            $table->integer('total_messages')->default(0);
            $table->integer('total_reviews')->default(0);
            $table->integer('done_reviews')->default(0);
            $table->integer('total_items')->default(0);
            $table->integer('total_orders')->default(0);
            $table->integer('remaining_reviews')->default(0);
            $table->double('average_rating')->default(0);
            $table->double('total_cost_current_month')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stats');
    }
};
