<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tickets', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('subject');
            $table->text('message');
            $table->enum('status', ['open','answered','in_progress', 'closed', 'on_hold', 'resolved', 'reopened', 'canceled'])->default('open');
            $table->timestamp('resolved_at')->nullable();
            $table->string('priority')->default('normal'); // normal, high, urgent
            $table->string('assigned_to')->nullable(); // User ID or name of the person assigned to the ticket
            $table->string('category')->nullable(); // Category of the ticket, e.g., 'technical', 'billing', etc.
            $table->string('attachment')->nullable(); // Path to any attachment related to the ticket
            $table->string('topic')->nullable(); // Topic of the ticket, e.g., 'login issue', 'payment problem', etc.
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tickets');
    }
};
