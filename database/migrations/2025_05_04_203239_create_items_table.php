<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('order_id')->constrained('orders')->onDelete('cascade');
            $table->string('name')->nullable();
            $table->string('sku')->nullable();
            $table->float('price')->nullable();
            $table->float('quantity')->nullable();
            $table->boolean('message_sent')->default(0);
            $table->boolean('has_review')->default(0);
            $table->string('variant_id')->nullable();
            $table->string('product_id')->nullable();
            $table->string('img_url')->nullable();
            $table->string('url')->nullable();
            $table->unique(['order_id', 'product_id']);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('items');
    }
};
