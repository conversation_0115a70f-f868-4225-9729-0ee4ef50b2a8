<?php

namespace Database\Seeders;

use App\Enums\Permissions\Message;
use App\Enums\Permissions\Review;
use App\Models\Plan;
use Illuminate\Database\Seeder;

class Plans extends Seeder
{
    protected function basePlanAttributes(): array
    {
        return [
            'type' => 'RECURRING',
            'trial_days' => 0,
            'test' => !app()->isProduction(),
            'on_install' => true,
            'updated_at' => now(),
            'created_at' => now(),
        ];
    }

    protected function planDefinitions(): array
    {
        return [
            [
                'name' => 'Free Trial',
                'price' => 0.00,
                'capped_amount' => 0.01,
                'interval' => 'EVERY_30_DAYS',
                'terms' => 'Free 5 reviews',
                'type' => 'RECURRING',
                'trial_days' => 30,
                'items_count' => 5,
                'billing_period'=>'monthly',
                'on_install' => true,
                'permissions' => [
                    Message::SEND->value,
                    Message::AUTO_SEND->value,
                    Review::AUTO_PUBLISH->value,
                    Review::PUBLISH->value,
                ],
            ],
            // Monthly Plans
            [
                'name' => 'Starter',
                'price' => 9.00,
                'capped_amount' => 9.00,
                'interval' => 'EVERY_30_DAYS',
                'terms' => '$9/month or $105/year and save 3%',
                'items_count' => 100,
                'billing_period'=>'monthly',
                'permissions' => [
                    Message::SEND->value,
                    Message::AUTO_SEND->value,
                    Review::AUTO_PUBLISH->value,
                    Review::PUBLISH->value,
                ],
            ],
            [
                'name' => 'Growth',
                'price' => 29.00,
                'capped_amount' => 29.00,
                'interval' => 'EVERY_30_DAYS',
                'terms' => '$29/month or $330/year and save 5%',
                'items_count' => 350,
                'billing_period'=>'monthly',
                'permissions' => [
                    Message::SEND->value,
                    Message::AUTO_SEND->value,
                    Review::AUTO_PUBLISH->value,
                    Review::PUBLISH->value,
                ],
            ],
            [
                'name' => 'Enterprise',
                'price' => 99.00,
                'capped_amount' => 99.00,
                'interval' => 'EVERY_30_DAYS',
                'terms' => '$99/month or $1100/year and save 7%',
                'items_count' => 1500,
                'billing_period'=>'monthly',
                'permissions' => [
                    Message::SEND->value,
                    Message::AUTO_SEND->value,
                    Review::AUTO_PUBLISH->value,
                    Review::PUBLISH->value,
                ],
            ],

            // Yearly Plans
            [
                'name' => 'Starter',
                'price' => 105.00,
                'capped_amount' => 105.00,
                'interval' => 'ANNUAL',
                'terms' => 'Starter yearly terms',
                'items_count' => 100,
                'billing_period'=>'yearly',
                'permissions' => [
                    Message::SEND->value,
                    Message::AUTO_SEND->value,
                    Review::AUTO_PUBLISH->value,
                    Review::PUBLISH->value,
                ],
            ],
            [
                'name' => 'Growth',
                'price' => 330.00,
                'capped_amount' => 330.00,
                'interval' => 'ANNUAL',
                'terms' => 'Growth yearly terms',
                'items_count' => 350,
                'billing_period'=>'yearly',
                'permissions' => [
                    Message::SEND->value,
                    Message::AUTO_SEND->value,
                    Review::AUTO_PUBLISH->value,
                    Review::PUBLISH->value,
                ],
            ],
            [
                'name' => 'Enterprise',
                'price' => 1100.00,
                'capped_amount' => 1100.00,
                'interval' => 'ANNUAL',
                'terms' => 'Enterprise yearly terms',
                'items_count' => 1500,
                'billing_period'=>'yearly',
                'permissions' => [
                    Message::SEND->value,
                    Message::AUTO_SEND->value,
                    Review::AUTO_PUBLISH->value,
                    Review::PUBLISH->value,
                ],
            ],
        ];
    }

    public function run(): void
    {
        foreach ($this->planDefinitions() as $plan) {
            Plan::updateOrCreate(
                ['price' => $plan['price']],
                [...$this->basePlanAttributes(), ...$plan]
            );
        }
    }
}
