name: Deploy-Prod

on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest

    env:
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}  # GitHub Actions token
      REPO_PATH: ${{ secrets.REPO_PATH }}  # Full repository path (user/repo) mucan54/myrepo
      BRANCH: "main"
      PROJECT_DIR: ${{ secrets.PROJECT_DIR_PROD }}  # Target directory on the server
      SERVER_IP: ${{ secrets.SERVER_IP }}  # IP address of the server
      SERVER_USER: ${{ secrets.SERVER_USER_PROD }}  # SSH user on the server

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Set up SSH
        uses: webfactory/ssh-agent@v0.5.3
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

      - name: Add known hosts
        run: |
          mkdir -p ~/.ssh
          touch ~/.ssh/known_hosts
          ssh-keyscan -T 30 -H $SERVER_IP >> ~/.ssh/known_hosts
      - name: Test SSH Connection
        run: |
          ssh -o StrictHostKeyChecking=no -v $SERVER_USER@$SERVER_IP 'echo "SSH connection successful"'
      - name: Pull latest changes or clone repo
        run: |
          ssh -o StrictHostKeyChecking=no $SERVER_USER@$SERVER_IP << EOF
            # Exporting variables inside the SSH session to ensure they are available
            export GITHUB_TOKEN=${GITHUB_TOKEN}
            export REPO_PATH=${REPO_PATH}
            export BRANCH=${BRANCH}
            export PROJECT_DIR=${PROJECT_DIR}
            export SERVER_USER_PROD=${SERVER_USER_PROD}
            # Check if the project directory exists
            if [ ! -d "\$PROJECT_DIR" ]; then
              echo "Directory does not exist, creating directory and cloning the repository."
              mkdir -p "\$PROJECT_DIR"
              cd "\$PROJECT_DIR"
              git clone https://x-access-token:\$<EMAIL>/\$REPO_PATH.git .
              git checkout \$BRANCH
              if [ -f "install.sh" ]; then
                echo "install.sh found, running the script."
                chmod +x install.sh
                ./install.sh
              else
                echo "install.sh not found, exiting."
              fi
            else
              cd "\$PROJECT_DIR"
              if [ -d ".git" ]; then
                echo "Git repository found, pulling the latest changes."
                git config core.fileMode false
                git pull https://x-access-token:\$<EMAIL>/\$REPO_PATH.git \$BRANCH
              else
                echo "Directory is not a Git repository. Cleaning up and cloning fresh."
                rm -rf *
                git clone https://x-access-token:\$<EMAIL>/\$REPO_PATH.git .
                git checkout \$BRANCH
                if [ -f "install.sh" ]; then
                  echo "install.sh found, running the script."
                  chmod +x install.sh
                  ./install.sh
                else
                  echo "install.sh not found, exiting."
                fi
              fi
            fi
          EOF
      - name: Check and run deploy.sh
        run: |
          ssh -o StrictHostKeyChecking=no $SERVER_USER@$SERVER_IP << EOF
            export PROJECT_DIR=${PROJECT_DIR}
            export SERVER_USER_PROD=${SERVER_USER_PROD}
            cd "\$PROJECT_DIR"
            if [ -f "deploy.sh" ]; then
              echo "deploy.sh found, running the script."
              chmod +x deploy.sh
              ./deploy.sh
            else
              echo "deploy.sh not found, exiting."
            fi
          EOF
