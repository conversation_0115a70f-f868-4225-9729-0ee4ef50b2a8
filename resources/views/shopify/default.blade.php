<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Commentier') }}</title>

    <!-- ✅ AppBridge -->
    <script src="https://unpkg.com/@shopify/app-bridge@3"></script>

    <!-- ✅ Tailwind CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet" />

    <!-- ✅ Livewire Styles -->
    @livewireStyles

    <!-- ✅ Alpine.js -->
    <!-- Only if not already bundled in app.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <script>
        // ✅ Redirect to Shopify Admin if opened outside iframe
        if (window.top === window.self) {
            const params = new URLSearchParams(window.location.search);
            const redirectUrl = `https://admin.shopify.com/store/{{ explode('.', Auth::user()->name)[0] }}/apps/commentier?${params.toString()}`;
            window.location.assign(redirectUrl);
        }

        // ✅ Initialize App Bridge safely
        document.addEventListener('DOMContentLoaded', function () {
            try {
                const AppBridge = window['app-bridge'];
                const createApp = AppBridge.default;

                if (!createApp) {
                    console.error('App Bridge is not available');
                    return;
                }

                const app = createApp({
                    apiKey: '{{ config('shopify-app.api_key') }}',
                    host: new URLSearchParams(location.search).get("host"),
                    forceRedirect: true,
                });

                window.app = app;
            } catch (e) {
                console.warn('App Bridge failed to initialize', e);
            }
        });
    </script>

    @yield('styles')
</head>

<body class="bg-gray-50 text-gray-900">
<div class="min-h-screen">
    <div class="border-b bg-white shadow px-4 py-3">
        <ui-nav-menu>
            <a href="{{ route('home') }}">Home</a>
            <a href="{{ route('queue.index') }}">Message Queue</a>
            <a href="">Comments</a>
            <a href="">Settings</a>
        </ui-nav-menu>
    </div>

    <main class="p-4">
        @yield('content')
    </main>
</div>

@livewireScripts
@yield('scripts')
</body>
</html>
