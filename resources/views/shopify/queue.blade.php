@extends('shopify.default')

<?php
$messages = collect([
    (object)[
        'customer_name' => '<PERSON>',
        'product_title' => 'Wireless Headphones',
        'scheduled_at' => '2025-05-01 10:00:00',
        'status' => 'pending',
    ],
    (object)[
        'customer_name' => '<PERSON>',
        'product_title' => 'Bluetooth Speaker',
        'scheduled_at' => '2025-05-01 09:30:00',
        'status' => 'sent',
    ],
    (object)[
        'customer_name' => '<PERSON>',
        'product_title' => 'Smart Watch',
        'scheduled_at' => '2025-05-01 08:45:00',
        'status' => 'pending',
    ],
    (object)[
        'customer_name' => '<PERSON>',
        'product_title' => 'Fitness Tracker',
        'scheduled_at' => '2025-04-30 17:00:00',
        'status' => 'sent',
    ],
    (object)[
        'customer_name' => '<PERSON>',
        'product_title' => 'Noise Cancelling Earbuds',
        'scheduled_at' => '2025-04-30 15:20:00',
        'status' => 'pending',
    ],
]);
?>

@section('content')
    <ui-title-bar title="Message Queue"></ui-title-bar>

    <div class="max-w-6xl mx-auto py-10 px-4" x-data="{ rescheduleId: null, showModal: false }">
        <h2 class="text-2xl font-bold text-gray-800 mb-6">Scheduled Messages</h2>

        <div class="overflow-x-auto">
            <table class="min-w-full bg-white rounded-lg shadow overflow-hidden">
                <thead class="bg-gray-100 text-left">
                <tr>
                    <th class="px-6 py-3 text-sm font-medium text-gray-700">Customer</th>
                    <th class="px-6 py-3 text-sm font-medium text-gray-700">Product</th>
                    <th class="px-6 py-3 text-sm font-medium text-gray-700">Scheduled At</th>
                    <th class="px-6 py-3 text-sm font-medium text-gray-700">Status</th>
                    <th class="px-6 py-3 text-sm font-medium text-gray-700">Actions</th>
                </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                @forelse ($messages as $index => $message)
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">{{ $message->customer_name }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">{{ $message->product_title }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">{{ $message->scheduled_at }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="text-sm font-semibold {{ $message->status === 'pending' ? 'text-yellow-500' : 'text-green-600' }}">
                                {{ ucfirst($message->status) }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap space-x-2">
                            <button
                                    class="text-sm text-red-600 hover:underline"
                                    @click.prevent="if(confirm('Are you sure you want to cancel this message?')) { alert('Cancelled #{{ $index }}') }"
                            >
                                Cancel
                            </button>
                            <button
                                    class="text-sm text-blue-600 hover:underline"
                                    @click.prevent="rescheduleId = {{ $index }}; showModal = true;"
                            >
                                Reschedule
                            </button>
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="5" class="px-6 py-4 text-center text-gray-500">No messages in queue.</td>
                    </tr>
                @endforelse
                </tbody>
            </table>
        </div>

        <!-- Reschedule Modal -->
        <div x-show="showModal" class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50" style="display: none;">
            <div class="bg-white p-6 rounded-lg w-full max-w-md shadow-xl" @click.away="showModal = false">
                <h3 class="text-lg font-bold mb-4">Reschedule Message</h3>
                <input type="datetime-local" class="w-full border px-3 py-2 rounded mb-4" />
                <div class="flex justify-end space-x-3">
                    <button class="px-4 py-2 bg-gray-200 rounded" @click="showModal = false">Cancel</button>
                    <button class="px-4 py-2 bg-blue-600 text-white rounded" @click="alert('Rescheduled #'+rescheduleId); showModal = false">Save</button>
                </div>
            </div>
        </div>
    </div>
@endsection
