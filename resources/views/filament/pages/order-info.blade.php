<x-filament-panels::page>

<div class=" p-4 md:p-6">
   <div class="mb-8">
      <!-- Order Header Card -->
      <div class="rounded-xl mb-4 border border-gray-200 shadow-sm overflow-hidden bg-white text-black dark:bg-gray-800 dark:text-white">
        <div class="p-6">
          <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div class="flex flex-col sm:flex-row sm:items-center gap-4">
              <div class="flex-shrink-0 w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6 text-gray-600">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 10.5V6a3.75 3.75 0 10-7.5 0v4.5m11.356-1.993l1.263 12c.07.665-.45 1.243-1.119 1.243H4.25a1.125 1.125 0 01-1.12-1.243l1.264-12A1.125 1.125 0 015.513 7.5h12.974c.576 0 1.059.435 1.119 1.007zM8.625 10.5a.375.375 0 11-.75 0 .375.375 0 01.75 0zm7.5 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
                </svg>
              </div>
              <div>
                <div class="flex items-center gap-3">
                  <h1 class="text-2xl font-bold text-gray-900">{{ __('Order') }} #{{ substr($this->getRecord()->order_number, -14) }}</h1>
                  <span class="inline-flex items-center rounded-md {{ $this->getRecord()->statusColor() }} px-2 py-1 text-xs font-medium text-green-800 ring-1 ring-green-600/20 ring-inset"> {{ $this->getRecord()->statusLabel() }}</span>
                </div>
                <div class="flex items-center gap-4 mt-1">
                  <p class="text-gray-500 text-sm">{{ \Carbon\Carbon::now()->format('F d, Y') }}</p>
                </div>
                  <div class="flex items-center gap-4 mt-1">
                      <p class="text-gray-500 text-sm">{{ __('Message Schedule Date') }} : {{ $this->getRecord()->message_date_at }}</p>
                  </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  <div class="grid gap-6 md:grid-cols-3">
    <div class="bg-white dark:bg-gray-800 dark:text-white overflow-hidden rounded-lg border border-neutral-200 md:col-span-2">
      <div class="lg:col-span-2">
        <div class="text-black dark:bg-gray-800 dark:text-white rounded-lg border border-gray-200 overflow-hidden">
          <div class="p-4 border-b border-gray-200">
            <h2 class="text-lg font-medium">{{ __('Order Items') }}</h2>
          </div>
            @forelse ($this->getRecord()->items as $item )
                <div class="border-b border-gray-200 p-4">
                    <div class="flex gap-4">
                        @if($item->img_url)
                            <img loading="lazy" src="{{ $item->img_url }}" alt="{{ $item->name }}" class="w-16 h-16 object-cover rounded">
                        @else
                            <div class="w-16 h-16 bg-gray-200 rounded"></div>
                        @endif
                        <div class="flex-1">
                            <h3 class="font-medium">{{ $item->name }}</h3>
                            <div class="text-sm text-gray-500 mt-1 space-y-1">
                            <div class="flex flex-wrap gap-x-4 gap-y-1">
                                <span>{{ __('Quantity') }}: {{ $item->quantity }}</span>
                                <span>{{ __('SKU') }}: {{ $item->sku }}</span>
                            </div>
                            </div>
                            <div class="flex gap-2 mt-2">
                                {{-- Message status badge --}}
                                <span @class([
                                        'inline-flex items-center gap-1 rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset',
                                        'bg-green-50 text-green-700 ring-green-600/20' => $item->message_sent,
                                        'bg-yellow-50 text-yellow-800 ring-yellow-600/20' => ! $item->message_sent,
                                    ])>
                                        <svg xmlns="http://www.w3.org/2000/svg"
                                             fill="none" viewBox="0 0 24 24"
                                             stroke-width="1.5" stroke="currentColor"
                                             class="w-3 h-3">
                                          <path stroke-linecap="round" stroke-linejoin="round"
                                                d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75" />
                                        </svg>
                                        {{ $item->message_sent ? __('Sent') : __('Pending') }}
                                </span>
                                {{-- Review status badge --}}
                                <span @class([
                                        'inline-flex items-center gap-1 rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset',
                                        'bg-green-50 text-green-700 ring-green-600/20' => $item->has_review,
                                        'bg-yellow-50 text-yellow-800 ring-yellow-600/20' => ! $item->has_review,
                                    ])>
                                        <svg xmlns="http://www.w3.org/2000/svg"
                                             fill="none" viewBox="0 0 24 24"
                                             stroke-width="1.5" stroke="currentColor"
                                             class="w-3 h-3">
                                          <path stroke-linecap="round" stroke-linejoin="round"
                                                d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z" />
                                        </svg>
                                        {{ $item->has_review ? __('Received') : __('Pending') }}
                                    </span>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-medium">{{ $item->price }} {{ $this->getRecord()->currency }}</p>
                        </div>
                    </div>
                </div>
            @empty
                <div class="p-4 text-center text-gray-500">
                    {{ __('No order item found.') }}
                </div>
            @endforelse
        </div>
      </div>
    </div>
    <div class="bg-white text-black dark:bg-gray-800 dark:text-white rounded-lg border border-gray-200 overflow-hidden">
      <div class="p-4 border-b border-gray-200">
        <h2 class="text-lg font-medium">{{ __('Customer Information') }}</h2>
      </div>
      <div class="p-4 border-b border-gray-200">
        <div class="flex items-center gap-3">
          <div class="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center">
            <svg class="w-10 h-10 text-gray-500" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 12c2.7 0 4.8-2.1 4.8-4.8S14.7 2.4 12 2.4 7.2 4.5 7.2 7.2 9.3 12 12 12zm0 2.4c-3.2 0-9.6 1.6-9.6 4.8v1.2c0 .7.5 1.2 1.2 1.2h16.8c.7 0 1.2-.5 1.2-1.2v-1.2c0-3.2-6.4-4.8-9.6-4.8z"/>
            </svg>
          </div>
          <div>
            <h3 class="font-medium">{{ $this->getRecord()->customer_name ?? 'N/A' }}</h3>
          </div>
        </div>
        <div class="mt-4 space-y-3">
          <div class="flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 text-gray-400">
              <path stroke-linecap="round" stroke-linejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75" />
            </svg>
            <span>{{ $this->getRecord()->customer_email ?? 'N/A' }}</span>
          </div>
          <div class="flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 text-gray-400">
              <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 002.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 01-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 00-1.091-.852H4.5A2.25 2.25 0 002.25 4.5v2.25z" />
            </svg>
            <span>{{ $this->getRecord()->phone_number ?? 'N/A' }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
    <template id="messageBlock">
        <div class="message text-gray-300 px-4 py-3 border-b border-gray-700">
            <div class="flex items-center relative">
                <div class="w-1/6">
                    <img class="w-11 h-11 rounded-full" id="personHeadshot" src="">
                </div>
                <div class="w-5/6">
                    <div class="text-xl text-white" id="personName">Josh Peters</div>
                    <div class="text-sm truncate" id="messagePreview"></div>
                </div>
                <span class="absolute right-0 top-0 text-xs mt-1">13:00</span>
            </div>
        </div>
    </template>
    @if(count($this->getRecord()->messages))
    <div class="rounded-lg border border-gray-200 bg-white mt-3 dark:bg-gray-800 dark:text-white">
        <div class="p-4 border-b border-gray-200">
            <h2 class="text-lg font-medium">@lang('Conversation')</h2>
        </div>
        <div id="messageBody" class="relative overflow-y-auto max-h-[600px] p-4 space-y-4">
            @foreach($this->getRecord()->messages as $message)
                @if($message->sender == \App\Enums\SenderTypes::SYSTEM->value)
                    <div class="flex justify-end">
                        <div class="bg-green-100 text-gray-900 dark:bg-green-900 dark:text-white rounded-tl-lg rounded-bl-lg rounded-br-lg px-4 py-2 max-w-md relative">
                            <div class="pr-8">{{ $message->message }}</div>
                            <div class="flex items-center justify-end gap-1 text-xs mt-1 text-gray-600 dark:text-gray-300">
                                <span>{{ $message->getZonedTime() }}</span>

                                {{-- Status Icons --}}
                                @if($message->status == \App\Enums\MessageStatus::PENDING)
                                    {{-- Single gray tick for pending --}}
                                    <div class="tooltip-wrapper relative">
                                        <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        <div class="tooltip">
                                            <div class="bg-gray-900 dark:bg-gray-700 text-white text-xs rounded py-1.5 px-3 whitespace-nowrap shadow-lg border border-gray-700 dark:border-gray-600">
                                                {{ __('Message pending') }}
                                            </div>
                                        </div>
                                    </div>
                                @elseif($message->status == \App\Enums\MessageStatus::SENT)
                                    {{-- Double blue ticks for success --}}
                                    <div class="tooltip-wrapper relative">
                                        <div class="flex -space-x-1">
                                            <svg class="w-4 h-4 text-blue-500 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            <svg class="w-4 h-4 text-blue-500 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                        </div>
                                        <div class="tooltip">
                                            <div class="bg-gray-900 dark:bg-gray-700 text-white text-xs rounded py-1.5 px-3 whitespace-nowrap shadow-lg border border-gray-700 dark:border-gray-600">
                                                {{ __('Delivered') }}
                                            </div>
                                        </div>
                                    </div>
                                @elseif($message->status == \App\Enums\MessageStatus::INSUFFICIENT_BALANCE)
                                    {{-- Red exclamation for insufficient balance --}}
                                    <div class="tooltip-wrapper relative">
                                        <svg class="w-4 h-4 text-red-500 dark:text-red-400 cursor-help" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                        </svg>
                                        <div class="tooltip">
                                            <div class="bg-red-900 dark:bg-red-800 text-white text-xs rounded py-1.5 px-3 whitespace-nowrap shadow-lg border border-red-800 dark:border-red-700">
                                                {{ __('Failed: Insufficient balance') }}
                                            </div>
                                            <div class="tooltip-arrow border-t-red-900 dark:border-t-red-800"></div>
                                        </div>
                                    </div>
                                @elseif($message->status == \App\Enums\MessageStatus::INVALID_PHONE_NUMBER)
                                    {{-- Red exclamation for invalid phone --}}
                                    <div class="tooltip-wrapper relative">
                                        <svg class="w-4 h-4 text-red-500 dark:text-red-400 cursor-help" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                        </svg>
                                        <div class="tooltip">
                                            <div class="bg-red-900 dark:bg-red-800 text-white text-xs rounded py-1.5 px-3 whitespace-nowrap shadow-lg border border-red-800 dark:border-red-700">
                                                {{ __('Failed: Invalid phone number') }}
                                            </div>
                                            <div class="tooltip-arrow border-t-red-900 dark:border-t-red-800"></div>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                @else
                    <div class="flex">
                        <div class="bg-gray-100 text-gray-900 dark:bg-gray-700 dark:text-white rounded-tr-lg rounded-bl-lg rounded-br-lg px-4 py-2 max-w-md">
                            {{ $message->message }}
                            <div class="text-xs text-right mt-1 text-gray-600 dark:text-gray-400">{{ $message->getZonedTime() }}</div>
                        </div>
                    </div>
                @endif
            @endforeach
        </div>
    </div>
    @else
        <div class="rounded-lg border border-gray-200 bg-white mt-3 dark:bg-gray-800 dark:text-white">
            <div class="p-4 border-b border-gray-200">
                <h2 class="text-lg font-medium">@lang('Conversation')</h2>
            </div>
            <div class="p-4 text-center text-gray-500 min-h-2">
                {{ __('No messages found.') }}
            </div>
        </div>
    @endif
</div>
    <style>
        /* Custom tooltip styles with dark mode support */
        .tooltip {
            position: absolute;
            bottom: 100%;
            right: 0;
            margin-bottom: 0.5rem;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.2s, visibility 0.2s;
            z-index: 50;
        }

        .tooltip-wrapper:hover .tooltip {
            opacity: 1;
            visibility: visible;
        }

        /* Arrow for error tooltips */
        .tooltip-arrow {
            position: absolute;
            top: 100%;
            right: 0.5rem;
            width: 0;
            height: 0;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-top: 4px solid;
        }

    </style>
</x-filament-panels::page>