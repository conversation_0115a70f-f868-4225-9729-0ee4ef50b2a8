<x-filament-panels::page>
    {{-- Tabs for billing_period --}}
    <div class="flex justify-center mb-8">
            <div class="bg-gray-100 p-1 rounded-lg inline-flex items-center">
                <button 
                    wire:click="setBillingPeriod('monthly')"
                    class="px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 {{ $billingPeriod === 'monthly' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900' }}"
                >
                    {{ __('Monthly') }}
                </button>
                <button 
                    wire:click="setBillingPeriod('yearly')"
                    class="px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 flex items-center gap-2 {{ $billingPeriod === 'yearly' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900' }}"
                >
                    {{ __('Yearly') }}
                </button>
            </div>
        </div>

    {{-- Plan Cards --}}
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        @forelse ($plans as $plan)
            <div class="border rounded-xl shadow-sm p-6 flex flex-col justify-between {{ $plan->id == $currentPlanId ? 'ring-2 ring-primary' : '' }}">
                    <div>
                       <h2 class="text-xl font-bold mb-2">{{ $plan->name }}</h2>
                       <div class="flex items-baseline gap-1">
                            <span class="text-2xl font-bold text-gray-600 dark:text-white">
                                ${{  number_format($plan->price, 2)  }}
                            </span>
                            <span class="text-gray-600 dark:text-white">/{{$billingPeriod === 'yearly' ? __('year') : __('month')}}</span>
                        </div>
                     <p style="font-size: 0.875rem; color: #16a34a; font-weight: 500; margin-top: 0.75rem;">
                            @if ($billingPeriod === 'monthly')
                                <?php
                                    /** terms are one of the following:
                                     * Free 5 reviews
                                     * $9/month or $105/year and save 3%
                                     * $29/month or $330/year and save 5%
                                     * $99/month or $1100/year and save 7%
                                     */
                                    ?>
                             {{ $plan->terms }}
                            @else
                             {{ __('Billed once per year') }}: ${{ number_format($plan->price, 2) }}
                            @endif
                        </p>
                        
                        <hr style="margin: 1.25rem 0; border: none; height: 1px; background-color: #e5e7eb;" />
                        
                       <ul class=" dark:text-white text-sm text-gray-600 mb-4 space-y-1">
                           <li>{{ __('Collect :count reviews every month', ['count' => $plan->items_count]) }}</li>
                       </ul>
                    </div>
                @if ($plan->id == $currentPlanId)
                    <x-filament::button disabled color="success" class="w-full">
                        {{ __('Current Plan') }}
                    </x-filament::button>
                @else
                    <x-filament::button wire:click="upgrade({{ $plan->id }}, '{{ request()->get('host') }}')" color="primary" class="w-full">
                        {{ __('Upgrade') }}
                    </x-filament::button>
                @endif
            </div>
        @empty
            <div class="col-span-full text-center py-8">
                <p class="text-gray-500">{{ __('No') }} {{ $billingPeriod }} {{ __('plans available.') }}</p>
            </div>
        @endforelse
    </div>
</x-filament-panels::page>