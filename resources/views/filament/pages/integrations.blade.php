<x-filament::page>

    <div class="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
        @php
            $integrationEngine = app(\App\Models\Integrations\IntegrationsEngine::class);
        @endphp
        @foreach ($this->getIntegrations() as $integration)
            @php
                $code = $integration->getCode();
                $isInstalled = $integration->isInstalled();
                $isActive = $integration->isActive();

                $icon = $isActive ? 'heroicon-o-check-circle' : ($isInstalled ? 'heroicon-o-pause' : 'heroicon-o-plus-circle');
                $color = $isActive ? 'success' : ($isInstalled ? 'warning' : 'primary');
            @endphp

            <div class="relative overflow-hidden transition-all duration-300 border rounded-xl shadow-sm hover:shadow-md bg-white dark:bg-gray-800 dark:border-gray-700">
                <div class="relative flex items-center justify-center">
                    <div class="absolute top-1 left-3">
                        <x-filament::icon-button :icon="$icon" :color="$color" size="sm" class="rounded-full"/>
                    </div>
                    <div class="w-16 h-16 mb-3">
                        {!! $integration->getSvg() !!}
                    </div>
                </div>

                <div class="p-5">
                    <div class="flex flex-col items-center mb-4">
                        <h3 class="text-lg font-semibold text-center text-gray-800 dark:text-gray-100">
                            {{ $integration->getName() }}
                        </h3>
                    </div>

                    <div class="flex justify-center gap-2 mb-4">
                        <span class="px-2 py-1 text-xs font-medium rounded-full {{ $isInstalled ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' }}">
                            {{ $isInstalled ? __('✓ Installed') : __('✗ Not Installed') }}
                        </span>
                        <span class="px-2 py-1 text-xs font-medium rounded-full {{ $isActive ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300' }}">
                            {{ $isActive ? __('● Active') : __('○ Inactive') }}
                        </span>
                    </div>
                </div>

                <div class="flex border-t dark:border-gray-700 divide-x divide-gray-200 dark:divide-gray-700">
                    @if ($isInstalled)
                        <x-filament::button
                                wire:click="installIntegration('{{ $code }}')"
                                color="success"
                                class="w-1/2 rounded-none text-sm py-3"
                        >
                            {{ __('Configure') }}
                        </x-filament::button>
                        <x-filament::button
                                wire:click="toggleActivation('{{ $code }}')"
                                :color="$isActive ? 'warning' : 'primary'"
                                class="w-1/2 rounded-none text-sm py-3"
                        >
                            {{ $isActive ? __('Deactivate') : __('Activate') }}
                        </x-filament::button>
                    @else
                        <x-filament::button
                                wire:click="installIntegration('{{ $code }}')"
                                color="primary"
                                class="w-full rounded-none text-sm py-3"
                        >
                            {{ __('＋ Install') }}
                        </x-filament::button>
                    @endif
                </div>
            </div>
        @endforeach
    </div>

    <x-filament::modal id="install-integration" width="lg" slide-over wire:ignore.self>
        <x-slot name="header">
            <h2 class="text-lg font-bold">
                {{ app(\App\Models\Integrations\IntegrationsEngine::class)->getByCode($activeIntegrationCode)?->getName() }} {{ __('Settings') }}
            </h2>
        </x-slot>

        <x-slot name="slot">
            <div class="space-y-4">
                @foreach ($settingsData as $key => $label)
                    <div>
                        <label for="{{ $key }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            {{ $label }}
                        </label>
                        <input
                                type="text"
                                id="{{ $key }}"
                                wire:model.defer="settings.{{ $key }}"
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-300"
                        />
                    </div>
                @endforeach
            </div>
        </x-slot>

        <x-slot name="footer">
            <div class="flex justify-between gap-2">
                @if (! empty($settings))
                    <x-filament::button wire:click="promptRemoveIntegration('{{ $activeIntegrationCode }}')" color="danger">
                        {{ __('Remove') }}
                    </x-filament::button>
                @endif
                <x-filament::button wire:click="saveIntegrationSettings" color="success">
                    {{ __('Save Settings') }}
                </x-filament::button>
            </div>
        </x-slot>
    </x-filament::modal>

</x-filament::page>

@script
<script>
    window.addEventListener('confirm', event => {
        if(confirm(event.detail[0].text)) {
            @this.call(event.detail[0].onConfirmed, event.detail[0].data);
        }
    });
</script>
@endscript