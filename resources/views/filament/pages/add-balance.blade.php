<x-filament-panels::page>
    @php
        $merchant_id = config('pay.paytr.merchant_id');
        $merchant_ok_url = route('balance.topup.success');
        $merchant_fail_url = route('balance.topup.fail');

        srand(time());
        $merchant_oid = rand();
        $user_ip = request()->ip();

        $payment_type = "card";
        $currency = "USD";
        $test_mode = "0";
        $non_3d = "0";
        $installment_count = "";
        $card_type = "";
        $client_lang = "en";
        $non3d_test_failed = "0";
        $post_url = config('pay.paytr.base_url');
        $user_basket = htmlentities(json_encode([["Service Fee", "dynamic", 1]]));
    @endphp

    <div class="container mx-auto mt-10 max-w-3xl">
        <div class="bg-white rounded-xl shadow-md border p-6 space-y-8">

            <form method="POST" action="{{ $post_url }}" id="balance-topup-form" class="space-y-10">
                @csrf

                <!-- Amount -->
                <div class="space-y-4 pb-6">
                    <h3 class="text-lg font-semibold text-primary-700">{{ __('Amount to Load') }}</h3>
                    <div class="relative">
                        <span class="absolute left-3 top-15 text-gray-500 text-[18px] font-semibold" style="top: 14px;">$</span>
                        <input
                                style="padding-left: 25px;"
                                type="text"
                                name="payment_amount"
                                id="payment_amount"
                                required
                                oninput="updateAmountImpact()"
                                class="pl-25 pl-8 pr-4 py-3 block w-full text-lg font-medium text-gray-900 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                                placeholder="0.00"
                        >
                    </div>
                    <p class="text-sm text-gray-500">{{ __('Minimum amount: $5.00') }}</p>
                    <div id="amount-impact-message"
                         class="hidden rounded-md border px-4 py-2 text-sm font-medium mt-1"></div>
                </div>

                <!-- Personal Info -->
                <div class="space-y-4 pb-6">
                    <h3 class="text-lg font-semibold text-gray-800">{{ __('Personal Information') }}</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="user_name" class="block text-sm font-medium text-gray-700">{{ __('Full Name') }}</label>
                            <input type="text" id="user_name" name="user_name" placeholder="John Doe" required
                                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"/>
                        </div>
                        <div>
                            <label for="user_phone" class="block text-sm font-medium text-gray-700">{{ __('Phone Number') }}</label>
                            <input type="text" id="user_phone" name="user_phone" placeholder="****** 123 4567" required
                                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"/>
                        </div>
                        <div class="md:col-span-2">
                            <label for="user_address" class="block text-sm font-medium text-gray-700">{{ __('Address') }}</label>
                            <input type="text" id="user_address" name="user_address"
                                   placeholder="123 Main St, New York, NY" required
                                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"/>
                        </div>
                    </div>
                </div>

                <!-- Card Info -->
                <div class="space-y-4 pb-6">
                    <h3 class="text-lg font-semibold text-gray-800">{{ __('Payment Details') }}</h3>
                    <div class="space-y-6">
                        <div>
                            <label for="cc_owner" class="block text-sm font-medium text-gray-700">{{ __('Cardholder Name') }}</label>
                            <input type="text" id="cc_owner" name="cc_owner" placeholder="As shown on card" required
                                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"/>
                        </div>

                        <div>
                            <label for="card_number" class="block text-sm font-medium text-gray-700">{{ __('Card Number') }}</label>
                            <input type="text" id="card_number" name="card_number" placeholder="4111 1111 1111 1111"
                                   required
                                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"/>
                        </div>

                        <div class="grid grid-cols-3 gap-4">
                            <div>
                                <label for="expiry_month" class="block text-sm font-medium text-gray-700">MM</label>
                                <input type="text" id="expiry_month" name="expiry_month" placeholder="MM" required
                                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"/>
                            </div>
                            <div>
                                <label for="expiry_year" class="block text-sm font-medium text-gray-700">YY</label>
                                <input type="text" id="expiry_year" name="expiry_year" placeholder="YY" required
                                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"/>
                            </div>
                            <div>
                                <label for="cvv" class="block text-sm font-medium text-gray-700">CVV</label>
                                <input type="text" id="cvv" name="cvv" placeholder="123" required
                                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"/>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Hidden Fields -->
                <input type="hidden" name="merchant_id" value="{{ $merchant_id }}">
                <input type="hidden" name="user_ip" value="{{ $user_ip }}">
                <input type="hidden" name="merchant_oid" value="{{ $merchant_oid }}">
                <input type="hidden" name="email" value="{{ auth()->user()->email }}">
                <input type="hidden" name="payment_type" value="{{ $payment_type }}">
                <input type="hidden" name="currency" value="{{ $currency }}">
                <input type="hidden" name="test_mode" value="{{ $test_mode }}">
                <input type="hidden" name="non_3d" value="{{ $non_3d }}">
                <input type="hidden" name="merchant_ok_url" value="{{ $merchant_ok_url }}">
                <input type="hidden" name="merchant_fail_url" value="{{ $merchant_fail_url }}">
                <input type="hidden" name="user_basket" id="user_basket" value="{{ $user_basket }}">
                <input type="hidden" name="debug_on" value="1">
                <input type="hidden" name="client_lang" value="{{ $client_lang }}">
                <input type="hidden" name="non3d_test_failed" value="{{ $non3d_test_failed }}">
                <input type="hidden" name="installment_count" value="{{ $installment_count }}">
                <input type="hidden" name="card_type" value="{{ $card_type }}">
                <input type="hidden" name="paytr_token" id="paytr_token" value="">

                <!-- Submit -->
                <div>
                    <button
                            type="submit"
                            onclick="return preparePaytrToken();"
                            class="w-full py-3 px-4 bg-primary-600 hover:bg-primary-700 text-white font-semibold rounded-lg shadow-md transition duration-150 ease-in-out">
                        {{ __('🚀 Load Balance') }}
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function updateAmountImpact() {
            const amountInput = document.getElementById('payment_amount');
            const messageEl = document.getElementById('amount-impact-message');
            const raw = amountInput.value.replace(',', '.');
            const amount = parseFloat(raw);

            if (!isNaN(amount) && amount >= 5) {
                const units = (amount /  {{\App\Services\AppSettings::getMessageCost()}}).toFixed(0);
                messageEl.innerHTML = `You're adding <strong>$${amount.toFixed(2)}</strong>, which equals <strong>${units}</strong> messages.`;
                messageEl.classList.remove('hidden', 'bg-red-50', 'text-red-800', 'border-red-200');
                messageEl.classList.add('bg-green-50', 'text-green-800', 'border-green-200');
            } else {
                messageEl.innerHTML = `<strong>{{ __('⚠️ Minimum amount is $5.00') }}</strong>`;
                messageEl.classList.remove('hidden', 'bg-green-50', 'text-green-800', 'border-green-200');
                messageEl.classList.add('bg-red-50', 'text-red-800', 'border-red-200');
            }
        }

        function preparePaytrToken() {
            const raw = document.getElementById('payment_amount').value.replace(',', '.');
            const amount = parseFloat(raw);

            if (isNaN(amount) || amount < 5) {
                alert("{{ __('Minimum amount is $5.00') }}");
                return false;
            }

            const data = {
                user_ip: '{{ $user_ip }}',
                merchant_oid: '{{ $merchant_oid }}',
                email: '{{ auth()->user()->email ?? '<EMAIL>' }}',
                payment_amount: amount.toFixed(2),
                payment_type: '{{ $payment_type }}',
                installment_count: '',
                currency: '{{ $currency }}',
                test_mode: '{{ $test_mode }}',
                non_3d: '{{ $non_3d }}',
            };

            fetch('/paytr-token', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
                .then(res => res.json())
                .then(response => {
                    document.getElementById('paytr_token').value = response.token;
                    document.getElementById('user_basket').value = JSON.stringify([["Service Fee", amount.toFixed(2), 1]]);
                    document.getElementById('balance-topup-form').submit();
                });

            return false;
        }
    </script>
</x-filament-panels::page>
