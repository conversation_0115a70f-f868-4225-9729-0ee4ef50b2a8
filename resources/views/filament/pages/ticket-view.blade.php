<div class="max-w-7xl mx-auto p-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div class="flex items-center space-x-6 dark:text-white">
            <a 
             wire:navigate 
             href="{{ $this->getResource()::getUrl('index') }}" 
             class="flex items-center  dark:text-white"
             >
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
                {{ __('Back to Tickets') }}
            </a>
            <h1 class="text-xl font-semibold  dark:text-white ml-2">#TKT-{{ $this->getRecord()->id }}</h1>
        </div>
        @if(auth()->user()?->is_super_admin)
        <!-- Super Admin Actions -->
        <div class="flex items-center space-x-3">     
            <!-- Assign to <PERSON> Button -->
            <button wire:click="assignToMe" class="flex items-center px-4 py-2 text-sm font-medium text-white" style="background-color: #25D366; border: none; border-radius: 0.375rem; transition: background-color 0.2s;">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                {{ __('Assign to Me') }}
            </button>
        </div>
        @endif
    </div>
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
       <!-- Main Content -->
       <div class="lg:col-span-2 space-y-6">
          <!-- Ticket Details -->
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 dark:bg-gray-800 dark:text-white">
             <h2 class="text-lg font-semibold  dark:text-white mb-3">{{$this->getRecord()->subject ?? 'N/A'}}</h2>
             <div class="flex items-center text-sm text-gray-600 mb-4">
                <span class="mr-4 dark:text-white">👤 {{$this->getRecord()->user?->name}}</span>
                <span class="dark:text-white">🕒 {{ __('Created') }}: {{ $this->getRecord()->created_at->format('F d, Y') }}</span>
             </div>
             <p class="text-gray-700 leading-relaxed dark:text-white">
                {{ $this->getRecord()->message ?? __('No description provided.') }}
             </p>
          </div>
          <!-- Conversation -->
          <div class="bg-white dark:bg-gray-800 dark:text-white rounded-lg shadow-sm border scrollbar-thumb-gray-400 p-6 relative h-64 overflow-y-scroll snap-y snap-mandatory dark:scrollbar-thumb-gray-700 dark:scrollbar-track-gray-800">
             <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold dark:bg-gray-800 dark:text-white">{{ __('Conversation') }}</h3>
                <span class="text-sm text-gray-500 dark:text-white">{{ $this->getRecord()->messages_count }} {{ __('replies') }}</span>
             </div>
             <div class="space-y-6">
                @forelse($this->getRecord()->messages as $message)
                @php $isCustomer = $message->user?->is_super_admin === 0; @endphp
                <div class="flex space-x-3">
                   <div class="w-8 h-8 {{ $isCustomer ? 'bg-gray-500' : 'bg-blue-500' }} rounded-full flex items-center justify-center text-white text-sm font-medium">
                      {{ strtoupper($message->user?->name[0] ?? '') }}
                   </div>
                   <div class="flex-1">
                      <div class="flex items-center space-x-2 mb-1">
                         <span class="font-medium dark:bg-gray-800 dark:text-white">{{ $message->user?->name }}</span>
                         @if(!$isCustomer)
                         <span class="text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded">{{ __('Support Agent') }}</span>
                         @endif
                         <span class="text-sm text-gray-500 dark:text-white">{{$message->created_at->format('F d, Y')}}</span>
                      </div>
                      <p class="text-gray-700 text-sm leading-relaxed dark:text-white">
                         {{ $message->message }}
                      </p>
                   </div>
                </div>
                @empty
                <div class="rounded-lg border border-gray-200 bg-white mt-3 dark:bg-gray-800 dark:text-white">
                   <div class="p-4 text-center text-gray-500 dark:text-white min-h-2">
                      {{ __('No messages found.') }}
                   </div>
                </div>
                @endforelse
             </div>
          </div>
          <!-- Add Reply -->
          @if($this->getRecord()->assigned_to || $this->getRecord()->user_id === auth()->id())
            <div class="bg-white  dark:bg-gray-800 dark:text-white rounded-lg shadow-sm border border-gray-200 p-4">
                <h3 class="text-lg dark:bg-gray-800 dark:text-white font-semibold dark:bg-gray-800 dark:text-white mb-4">{{ __('Add Reply') }}</h3>
                <form wire:submit.prevent="reply({{ $this->getRecord()->id }})" class="space-y-4">
                    <textarea wire:model="message"
                        class="w-full border dark:bg-gray-800 dark:text-white border-gray-300 rounded-md p-3 text-sm resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                        rows="4" 
                        placeholder="Type your reply here...">
                    </textarea>
                    @error('message') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    <div class="flex items-center justify-between mt-4">
                    <div class="flex space-x-3">
                        <button type="submit" class="bg-gray-900 text-white px-4 py-2 rounded-md hover:bg-gray-800 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                            </svg>
                            {{ __('Send Reply') }}
                        </button>
                    </div>
                    </div>
                </form>
            </div>
          @endif
       </div>
       <!-- Sidebar placeholder -->
       <div class="lg:col-span-1">
          <div class="bg-white text-black dark:bg-gray-800 dark:text-white rounded-lg border bg-card text-card-foreground shadow-sm" data-v0-t="card">
             <div class="flex flex-col space-y-1.5 p-6">
                <h3 class="text-2xl font-semibold leading-none tracking-tight">{{ __('Ticket Information') }}</h3>
             </div>
             <div class="p-6 pt-0 space-y-4">
                <div class="space-y-3">
                   <div class="flex items-center justify-between">
                      <span class="text-sm font-medium text-gray-600 dark:text-white">{{ __('Status') }}</span>
                      <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 hover:bg-primary/80 " data-v0-t="badge">  {{ ucwords(\App\Enums\TicketStatuses::labelFromValue($this->getRecord()->status)) ?? 'N/A' }}</div>
                   </div>
                   <div class="flex items-center justify-between">
                      <span class="text-sm font-medium text-gray-600 dark:text-white">{{ __('Priority') }}</span>
                      <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 hover:bg-primary/80" data-v0-t="badge">
                         <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-flag h-3 w-3 mr-1">
                            <path d="M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z"></path>
                            <line x1="4" x2="4" y1="22" y2="15"></line>
                         </svg>
                         {{ ucwords($this->getRecord()->priority) ?? 'N/A' }}
                      </div>
                   </div>
                   <div class="flex items-center justify-between">
                      <span class="text-sm font-medium text-gray-600 dark:text-white">{{ __('Topic') }}</span>
                      <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 text-foreground" data-v0-t="badge">
                         <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-tag h-3 w-3 mr-1">
                            <path d="M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z"></path>
                            <circle cx="7.5" cy="7.5" r=".5" fill="currentColor"></circle>
                         </svg>
                        {{ ucwords($this->getRecord()->topic) ?? 'N/A' }}
                      </div>
                   </div>
                   <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-600 dark:text-white">{{ __('Assigned to') }}:</span>
                    <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 text-foreground" data-v0-t="badge">
                        👤 {{ $this->getRecord()->assignedUser?->name ?? __('Unassigned') }}
                    </div>
                 </div>
                </div>
             </div>
          </div>
       </div>
    </div>
 </div>
 </div>