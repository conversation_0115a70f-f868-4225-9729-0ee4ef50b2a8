<x-filament-panels::page>
   <div class="py-6 px-4 ">
    <!-- Header Section -->
    <div class="bg-white text-black dark:bg-gray-800 dark:text-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
      <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-4">
        <div class="flex items-center gap-3">
          <h1 class="text-2xl font-bold text-gray-800 dark:text-white">{{ __('Review') }} #{{ $this->getRecord()->id }}</h1>
          <div class="flex items-center">
            <!-- SVG Star Rating -->
              @for ($i = 1; $i <= 5; $i++)
                  @if ($i <= $this->getRecord()->rating)
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="#21be5c" stroke="#21be5c" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5">
                      <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
                    </svg>
                  @else
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="#e2e8f0" stroke="#e2e8f0" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5">
                      <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
                    </svg>
                  @endif
              @endfor
          </div>
        </div>
        <div class="flex flex-wrap gap-2">
           <span @class([
                'inline-flex items-center gap-1 rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset',
                'bg-green-50 text-green-700 ring-green-600/20' => $this->getRecord()->is_published,
                'bg-yellow-50 text-yellow-800 ring-yellow-600/20' => ! $this->getRecord()->is_published,
            ])>
            {{$this->getRecord()->is_published ? __('Published') : __('Unpublished')}}
          </span>
        </div>
      </div>

      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
        <div class="flex items-center gap-2">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#21be5c" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
            <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
            <circle cx="12" cy="7" r="4"></circle>
          </svg>
          <span class="text-gray-500">{{ __('User') }}:</span>
          <a href="/users/5" class="font-medium hover:text-brand-primary transition-colors">
            {{ $this->getRecord()->order->customer_name ?? 'N/A' }}
          </a>
        </div>
        
        <div class="flex items-center gap-2">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#21be5c" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
            <rect width="18" height="18" x="3" y="4" rx="2" ry="2"></rect>
            <line x1="16" x2="16" y1="2" y2="6"></line>
            <line x1="8" x2="8" y1="2" y2="6"></line>
            <line x1="3" x2="21" y1="10" y2="10"></line>
          </svg>
          <span class="text-gray-500">{{ __('Created') }}:</span>
          <span>{{ $this->getRecord()->created_at->format('F d, Y') }}</span>
        </div>
        <div class="flex items-center gap-2">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#21be5c" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
            <circle cx="12" cy="12" r="10"></circle>
            <polyline points="12 6 12 12 16 14"></polyline>
          </svg>
          <span class="text-gray-500">{{ __('Updated') }}:</span>
          <span>{{ $this->getRecord()->updated_at->format('F d, Y') }}</span>
        </div>
      </div>
    </div>

    <!-- Rating & Review Content Section -->
    <div class="bg-white rounded-lg border border-gray-200 shadow-sm mb-6 text-black dark:bg-gray-800 dark:text-white ">
      <div class="border-b border-gray-200 bg-gray-50 py-3 px-6 dark:bg-gray-800 dark:text-white">
        <h2 class="text-lg flex items-center gap-2 text-gray-800 font-semibold dark:bg-gray-800 dark:text-white">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#21be5c" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5">
            <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
          </svg>
          {{ __('Review Content') }}
        </h2>
      </div>
      <div class="p-5 text-white dark:bg-gray-800 dark:text-white ">
        <div class="space-y-4">
          <div>
            <h3 class="font-semibold text-lg mb-2 text-gray-800 dark:bg-gray-800 dark:text-white ">{{$this->getRecord()->review_title ?? 'N/A'}}</h3>
            <p class="text-gray-700 dark:bg-gray-800 dark:text-white ">{{$this->getRecord()->review_description ?? 'N/A'}}</p>
          </div>
          <div class="flex items-center gap-2 bg-gray-50 p-3 rounded-md inline-flex dark:bg-gray-800 dark:text-white ">
            <span class="font-medium text-gray-700 dark:bg-gray-800 dark:text-white ">{{ __('Rating') }}:</span>
            <div class="flex items-center">
              <!-- SVG Star Rating -->
               @for ($i = 1; $i <= 5; $i++)
                  @if ($i <= $this->getRecord()->rating)
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="#21be5c" stroke="#21be5c" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5">
                      <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
                    </svg>
                  @else
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="#e2e8f0" stroke="#e2e8f0" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5">
                      <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
                    </svg>
                  @endif
                @endfor
            </div>
            <span class="text-gray-500 dark:bg-gray-800 dark:text-white ">({{$this->getRecord()->rating ?? 0}}/5)</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Related Information Sections in Grid Layout -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
      <!-- Item Details Section -->
      <div class="bg-white rounded-lg border border-gray-200 shadow-sm dark:bg-gray-800 dark:text-white">
        <div class="border-b border-gray-200 bg-gray-50 py-3 px-6 dark:bg-gray-800 dark:text-white">
          <h2 class="text-lg flex items-center gap-2 text-gray-800 font-semibold dark:bg-gray-800 dark:text-white">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#21be5c" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5">
              <path d="M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z"></path>
              <path d="m3.3 7 8.7 5 8.7-5"></path>
              <path d="M12 22V12"></path>
            </svg>
            {{ __('Item Details') }}
          </h2>
        </div>
        <div class="p-5">
          <div class="flex flex-col sm:flex-row gap-4 items-start">
            <div class="flex-shrink-0 border rounded-md p-1 bg-white dark:bg-gray-800 dark:text-white ">
              @if($this->getRecord()->item?->img_url)
                <img loading="lazy" src="{{$this->getRecord()->item?->img_url }}" alt="{{$this->getRecord()->item?->name }}" class="w-16 h-16 object-cover rounded">
              @else
                  <div class="w-16 h-16 bg-gray-200 rounded"></div>
              @endif
            </div>
            <div class="flex-grow space-y-3 ">
              <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-2 ">
                <h3 class="font-medium text-lg text-gray-800 dark:bg-gray-800 dark:text-white">{{$this->getRecord()->item?->name}}</h3>
              </div>
              <div class="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm ">
                <div class="bg-gray-50 p-3 rounded-md dark:bg-gray-800 dark:text-white">
                  <span class="text-gray-500 block mb-1 dark:bg-gray-800 dark:text-white">{{ __('SKU') }}</span>
                  <span class="font-medium text-gray-800 dark:bg-gray-800 dark:text-white">{{$this->getRecord()->item?->sku}}</span>
                </div>
                <div class="bg-gray-50 p-3 rounded-md dark:bg-gray-800 dark:text-white">
                  <span class="text-gray-500 block mb-1 dark:bg-gray-800 dark:text-white">{{ __('Quantity') }}</span>
                  <span class="font-medium text-gray-800 dark:bg-gray-800 dark:text-white">{{$this->getRecord()->item?->quantity}}</span>
                </div>
                <div class="bg-gray-50 p-3 rounded-md dark:bg-gray-800 dark:text-white">
                  <span class="text-gray-500 block mb-1 dark:bg-gray-800 dark:text-white">{{ __('Price') }}</span>
                  <span class="font-medium text-gray-800 dark:bg-gray-800 dark:text-white"> {{ number_format($this->getRecord()->item?->price) }} {{ $this->getRecord()->order?->currency }} x {{ $this->getRecord()->item?->quantity }}</span>
                </div>
                <div class="bg-gray-50 p-3 rounded-md dark:bg-gray-800 dark:text-white">
                  <span class="text-gray-500 block mb-1 dark:bg-gray-800 dark:text-white">{{ __('Total') }}</span>
                  <span class="font-medium text-gray-800 dark:bg-gray-800 dark:text-white">{{ number_format($this->getRecord()->item?->price * $this->getRecord()->item?->quantity, 2) }} {{ $this->getRecord()->order?->currency }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Order Info Section -->
      <div class="bg-white rounded-lg border border-gray-200 shadow-sm ">
        <div class="border-b border-gray-200 bg-gray-50 py-3 px-6 dark:bg-gray-800 dark:text-white">
          <h2 class="text-lg flex items-center gap-2 text-gray-800 font-semibold dark:bg-gray-800 dark:text-white">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#21be5c" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5">
              <path d="M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z"></path>
              <path d="m3.3 7 8.7 5 8.7-5"></path>
              <path d="M12 22V12"></path>
            </svg>
            {{ __('Order Information') }}
          </h2>
        </div>
        <div class="p-5 dark:bg-gray-800 dark:text-white">
          <div class="space-y-5 ">
            <div>
              <h3 class="font-medium text-gray-800 mb-3 dark:bg-gray-800 dark:text-white">{{ __('Order Details') }}</h3>
              <div class="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm">
                <div class="bg-gray-50 p-3 rounded-md dark:bg-gray-800 dark:text-white">
                  <span class="text-gray-500 block mb-1 dark:bg-gray-800 dark:text-white">{{ __('Order Number') }}</span>
                  <a href="/orders/3" class="font-medium text-gray-800 hover:text-brand-primary dark:bg-gray-800 dark:text-white">
                    #{{ substr($this->getRecord()->order?->order_number, -14) }}
                  </a>
                </div>
                <div class="bg-gray-50 p-3 rounded-md dark:bg-gray-800 dark:text-white">
                  <span class="text-gray-500 block mb-1 dark:bg-gray-800 dark:text-white">{{ __('Customer') }}</span>
                  <span class="font-medium text-gray-800 dark:bg-gray-800 dark:text-white">
                    {{ $this->getRecord()->order?->customer_name }} @if($this->getRecord()->order?->customer_email) ({{ $this->getRecord()->order?->customer_email }}) @endif
                  </span>
                </div>
                <div class="bg-gray-50 p-3 rounded-md dark:bg-gray-800 dark:text-white">
                  <span class="text-gray-500 block mb-1 dark:bg-gray-800 dark:text-white">{{ __('Total Amount') }}</span>
                  <span class="font-medium text-gray-800 dark:bg-gray-800 dark:text-white">
                    {{ number_format($this->getRecord()->order?->total_amount, 2) }} {{ $this->getRecord()->order?->currency }}
                  </span>
                </div>
                <div class="bg-gray-50 p-3 rounded-md dark:bg-gray-800 dark:text-white">
                  <span class="text-gray-500 block mb-1 dark:bg-gray-800 dark:text-white">{{ __('Status') }}</span>
                  <span class="inline-flex items-center rounded-md dark:bg-gray-800 dark:text-white {{ $this->getRecord()->order?->statusColor() }} px-2.5 py-0.5 text-xs font-medium text-brand-primary border border-brand-border">
                   {{ $this->getRecord()->order?->statusLabel() }}
                  </span>
                </div>
              </div>
            </div>
            {{-- <hr class="border-gray-200"> --}}
          </div>
        </div>
      </div>
    </div>

   </div>   
</x-filament-panels::page>