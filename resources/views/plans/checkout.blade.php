@extends('layouts.app')

@section('title', 'Checkout – ' . $plan['name'] . ' Plan')

@section('content')
    <div class="max-w-xl mx-auto py-16 px-4">
        <h2 class="text-2xl font-bold mb-6 text-center">Checkout – {{ $plan['name'] }} Plan</h2>

        @if(session('error'))
            <div class="text-red-600 mb-4">{{ session('error') }}</div>
        @endif

        <form id="payment-form" method="POST" action="{{ route('checkout.process', $key) }}">
            @csrf

            {{-- Billing Details --}}
            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-3">Billing Information</h3>

                <div class="mb-4">
                    <label class="block text-sm font-medium mb-1">Full Name</label>
                    <input type="text" name="name" required class="w-full border border-gray-300 rounded-lg p-2">
                </div>

                <div class="mb-4">
                    <label class="block text-sm font-medium mb-1">Email Address</label>
                    <input type="email" name="email" required class="w-full border border-gray-300 rounded-lg p-2">
                </div>

                <div class="mb-4">
                    <label class="block text-sm font-medium mb-1">Phone Number</label>
                    <input type="tel" name="phone" required class="w-full border border-gray-300 rounded-lg p-2">
                </div>

                <div class="mb-4">
                    <label class="block text-sm font-medium mb-1">Address</label>
                    <input type="text" name="address" required class="w-full border border-gray-300 rounded-lg p-2">
                </div>

                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium mb-1">City</label>
                        <input type="text" name="city" required class="w-full border border-gray-300 rounded-lg p-2">
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-1">ZIP / Postal Code</label>
                        <input type="text" name="zip" required class="w-full border border-gray-300 rounded-lg p-2">
                    </div>
                </div>

                <div class="mt-4">
                    <label class="block text-sm font-medium mb-1">Country</label>
                    <input type="text" name="country" required class="w-full border border-gray-300 rounded-lg p-2">
                </div>
            </div>

            {{-- Card Details --}}
            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-3">Card Information</h3>

                <div class="mb-4">
                    <label class="block text-sm font-medium mb-1">Cardholder Name</label>
                    <input type="text" name="card_holder_name" placeholder="Name on card" required class="w-full border border-gray-300 rounded-lg p-2">
                </div>

                <div class="mb-4">
                    <label class="block text-sm font-medium mb-1">Card Number</label>
                    <input type="text" name="card_number" placeholder="1234 5678 9012 3456" required class="w-full border border-gray-300 rounded-lg p-2" autocomplete="off">
                </div>

                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium mb-1">Expiry Date (MM/YY)</label>
                        <input type="text" name="card_expiry" placeholder="12/24" required class="w-full border border-gray-300 rounded-lg p-2" autocomplete="off">
                    </div>

                    <div>
                        <label class="block text-sm font-medium mb-1">CVC</label>
                        <input type="text" name="card_cvc" placeholder="123" required class="w-full border border-gray-300 rounded-lg p-2" autocomplete="off">
                    </div>
                </div>
            </div>

            {{-- Submit --}}
            <button type="submit" class="w-full py-3 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700">
                Pay ${{ number_format($plan['price'] / 100, 2) }}
            </button>
        </form>
    </div>
@endsection
